<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Str;


class ImpersonateUserController extends Controller
{
    public function impersonate($user_id)
    {
        //dd(Auth::user());
        if (Auth::user()->email == '<EMAIL>') {
            $newUser = User::find($user_id);
           // session()->put('impersonate', $user_id);
            $impersonate = Auth::user()->impersonate($newUser, 'web');
            return Redirect::to(config('app.url'));
        }
        elseif(Auth::user()->admin){

            return response(['url'=>config('app.url').'users/'.$user_id.'/impersonate']);
        }
        else
            return response('unauthorized',403);

    }
}
