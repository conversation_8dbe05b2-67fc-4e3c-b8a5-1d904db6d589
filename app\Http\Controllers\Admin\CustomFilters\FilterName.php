<?php

namespace App\Http\Controllers\Admin\CustomFilters;

use Illuminate\Database\Eloquent\Builder;
use Spatie\QueryBuilder\Filters\Filter;

class FilterName implements Filter
{
    public function __invoke(Builder $query, $value, string $property)
    {
        $query->whereHas('user', function ($contest_query) use ($value) {
            $contest_query->where('name', 'LIKE', '%' . $value . '%');

        });

    }

}
