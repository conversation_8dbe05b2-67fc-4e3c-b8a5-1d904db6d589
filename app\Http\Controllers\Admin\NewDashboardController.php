<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\Subscription;


class NewDashboardController extends Controller
{
    public function index()
    {
        $data['subscriptionsToday'] = $this->getSubscriptionsToday();
        $data['subscriptionsYesterday'] = $this->getSubscriptionsYesterday();
        $data['subscriptionsLastWeek'] = $this->getSubscriptionsLastWeek();
        $data['subscriptionsLastMonth'] = $this->getSubscriptionsLastMonth();
        $data['activeSubscriptionCount'] = $this->getActiveSubscriptionCount();
        $data['usersByLatestSubscription'] = $this->getUsersByLatestSubscription();
        $data['topConsumptionUsers'] = $this->getUsersByConsumption();
        $data['subscriptionsLastYear'] = $this->getSubscriptionsLastYear();
        $data['chart'] = $this->getChartSubscription();

        return response(['success' => true, 'data' => $data]);
    }

    public function getSubscriptionsToday()
    {
        $subscriptions = Subscription::with('user')
            ->whereDate('created_at', now()->format('Y-m-d'))
            ->distinct()
            ->get();

        $topSubscriptions = $subscriptions->take(10);
        return $topSubscriptions;
    }

    public function getSubscriptionsYesterday()
    {
        $yesterday = new DateTime('yesterday');
        $subscriptions = Subscription::with('user')
            ->whereDate('created_at', $yesterday
                ->format('Y-m-d'))
            ->distinct()
            ->get();

        $topSubscriptions = $subscriptions->take(10);
        return $topSubscriptions;
    }

    public function getSubscriptionsLastWeek()
    {
        $startOfLastWeek = now()->subWeek()->startOfWeek();
        $endOfLastWeek = now()->subWeek()->endOfWeek();
        $subscriptions = Subscription::with('user')
            ->whereBetween('created_at', [$startOfLastWeek, $endOfLastWeek])
            ->distinct()
            ->get();

        $topSubscriptions = $subscriptions->take(10);
        return $topSubscriptions;
    }

    public function getSubscriptionsLastMonth()
    {
        $start = new DateTime('first day of last month');
        $end = new DateTime('last day of last month');
        $subscriptions = Subscription::with('user')
            ->whereBetween('created_at', [$start, $end])
            ->distinct()
            ->limit(10)
            ->get();

        return $subscriptions;
    }

    public function getSubscriptionsLastYear()
    {
        $subscriptions = Subscription::with('user')
            ->whereHas('user')
            ->whereBetween('created_at', [now()->startOfYear(), now()->endOfDay()])
            ->latest()
            ->limit(10)
            ->distinct('billable_id')
            ->get();
        return $subscriptions;
    }


    public function getActiveSubscriptionCount()
    {
        $activeSubscriptions = Subscription::where('paddle_status', 'active')->count();

        return $activeSubscriptions;
    }

    public function getUsersByLatestSubscription()
    {
        $subscription = Subscription::with('user')
            ->latest()
            ->distinct()
            ->limit(10)
            ->get();

        return $subscription;
    }

    function getUsersByConsumption()
    {
        $users = User::where('admin', 0)->orderBy('total_consumption', 'DESC')->limit(10)->get();

//        $sortedUsers = $users->sortByDesc(function ($user) {
//            return $user->consumption();
//        });

        return $users;
    }

    public function showSubscription(Subscription $subscription)
    {
        return response(['success' => true, 'data' => $subscription]);
    }

    public function showUser(User $user)
    {
        return response(['success' => true, 'data' => $user]);
    }

    private function getChartSubscription()
    {
        $plans = ['Premium Plan', 'Standard Plan', 'Enterprise Plan'];
        $planKeys = array_map(function ($plan) {
            return lcfirst(str_replace(' ', '_', $plan));
        }, $plans);

        $startOfYear = Carbon::now()->startOfYear();
        $endOfNow = Carbon::now();

        $months = [];
        $period = Carbon::parse($startOfYear)->startOfMonth();
        while ($period <= $endOfNow) {
            $months[] = $period->format('Y-m');
            $period->addMonth();
        }

        $chartData = [];

        foreach ($months as $month) {
            $start = Carbon::createFromFormat('Y-m', $month)->startOfMonth();
            $end = Carbon::createFromFormat('Y-m', $month)->endOfMonth();

            $monthRow = ['month' => $month];

            foreach ($plans as $plan) {
                $key = lcfirst(str_replace(' ', '_', $plan));
                $total = Subscription::where('paddle_plan', $plan)
                    ->whereBetween('created_at', [$start, $end])
                    ->sum('amount');

                $monthRow[$key] = $total;
            }

            $chartData[] = $monthRow;
        }

        return $chartData;
    }
}
