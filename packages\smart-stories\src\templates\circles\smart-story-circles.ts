import { html, css } from "lit";
import { state, property } from 'lit/decorators.js';
import { BaseSmartStory } from "../../base-smart-story";

class SmartStoryCircles extends BaseSmartStory {
  @state() playSpinAnimation = false;
  @state() videoLoaded = false;
  @property() thumbnailOrder: number;
  @state() sequenceCounter = 0;
  @property({ type: Number }) totalStories = 0;
    intervalId:any = null;

  constructor() {
    super();
    this.intervalId = null;
  }

  connectedCallback() {
    super.connectedCallback();
    if (this.totalStories > 0) {
      this.intervalId = setInterval(() => {
        this.sequenceCounter = (this.sequenceCounter + 1) % this.totalStories;
      }, 5000);
    }
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  shouldPlayVideo() {
    const mode = Number(this.settings?.thumbnailPlaybackMode);

    switch (mode) {
      case 0:
        return false;
      case 1:
                // Only play if this is the first story
        return this.thumbnailOrder === 0;
      case 2:
                // Only play if this story is currently active in the sequence
        return this.thumbnailOrder === this.sequenceCounter;
      case 3:
                // Play all videos simultaneously
        return true;
      default:
        return false;
    }
  }

  static styles = [BaseSmartStory.getStyles(), css`
    @keyframes spinTimeLine {
      0%   {--spinDegree:0deg;}
      10%   {--spinDegree:36deg;}
      20%   {--spinDegree:72deg;}
      30%   {--spinDegree:108deg;}
      40%   {--spinDegree:144deg;}
      50%   {--spinDegree:180deg;}
      60%   {--spinDegree:216deg;}
      70%   {--spinDegree:252deg;}
      80%   {--spinDegree:288deg;}
      90%   {--spinDegree:324deg;}
      100%   {--spinDegree:360deg;}
    }

    :host {
      display: flex;
    }

    .circle-container {
      position: relative;
      width: 100%;
      /* height: 100%;  please don't play with height it will affect ios devices!!!!!!!!!!!!!!!!! */
      overflow: hidden;
      clip-path: circle(50%);
    }

    .story-name-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 5px;
      text-align: center;
      color: white;
      background: rgba(0, 0, 0, 0.1);
      text-shadow: 0 0 5px rgba(0, 0, 0, 0.7);
      line-height: 1.2;
    }

    img {
      width: 100%;
      /* height: 100%;   please don't play with height it will affect ios devices!!!!!!!!!!!!!!!!! */
      object-fit: cover;
      object-position: center;
    }
  `];

  _handleVideoLoaded() {
    this.videoLoaded = true;
  }

  render() {
    let backgroundStyle = `background:linear-gradient(72deg, ${this.settings?.primary_color} 50%, ${this.settings?.secondary_color} 100%);`;

    if (this.settings?.instagram_colors) {
      backgroundStyle = `background: radial-gradient(circle at 30% 107%, #fdf497 0%, #fdf497 5%, #fd5949 45%, #d6249f 60%);`;
    }

    const spinBackgroundStyle = this.settings?.instagram_colors
      ? `background: radial-gradient(circle at 30% 107%, #fdf497 0%, #fdf497 5%, #fd5949 45%, #d6249f 60%);`
      : `background:linear-gradient(var(--spinDegree), ${this.settings?.primary_color} 50%, ${this.settings?.secondary_color} 100%);`;

    const horizontalAlignment = this.settings?.thumbIcon_HorizontalAlignment || "2";
    const verticalAlignment = this.settings?.thumbIcon_VerticalAlignment || "2";

    const alignmentClasses = `
      ${horizontalAlignment === "0" ? "left-2" : horizontalAlignment === "1" ? "right-2" : "left-1/2 transform -translate-x-1/2"}
      ${verticalAlignment === "0" ? "top-2" : verticalAlignment === "1" ? "bottom-2" : "top-1/2 transform -translate-y-1/2"}
    `;

    const hidePlayIcon = this.settings?.hide_play_icon ?? true;
    const playIconSize = this.settings?.play_icon_size || 40;

    const placeholderImgSrc = this.story.thumbnail_resized
      ? this.story.thumbnail_resized
      : this.story.thumbnail_url
        ? this.story.thumbnail_url
        : `${this.appUrl}/images/placeholder/thumbnail.png`;

    const useV2 = (this.settings?.thumb_size ?? 0) >= 250;
    const videoUrl = useV2
      ? this.story.media[0].clip_video_v2_url
      : this.story.media[0].clip_video_v1_url;

    const mediaContent = this.settings?.thumbnailPlaybackMode !== 0 && videoUrl && this.shouldPlayVideo()
      ? html`
          <div class="relative w-full h-full" style="aspect-ratio: 1 / 1;">
            <img
              alt=""
              loading="lazy"
              style="aspect-ratio: 1 / 1;"
              class="w-full object-cover object-center ${this.videoLoaded ? 'hidden' : ''}"
              src="${placeholderImgSrc}"
            />
            <video
              ?autoplay=${this.shouldPlayVideo()}
              loop
              muted
              playsinline
              loading="lazy"
              src="${videoUrl}"
              class="w-full object-cover object-center ${this.videoLoaded ? '' : 'hidden'}"
              @loadeddata="${this._handleVideoLoaded}"
              style="aspect-ratio: 1 / 1;"
            ></video>
          </div>
        `
      : html`
          <img
            alt=""
            loading="lazy"
            style="aspect-ratio: 1 / 1;"
            class="w-full object-cover object-center"
            src="${placeholderImgSrc}"
          />
        `;

    // Determine if we need side positioning for layout
    const isLeftSide = (this.settings?.showStoryName ?? true) && this.settings?.storyNameDisplay === "4";
    const isRightSide = (this.settings?.showStoryName ?? true) && this.settings?.storyNameDisplay === "3";
    const needsSideLayout = isLeftSide || isRightSide;

    return html`
      <div class="flex h-full ${needsSideLayout ? 'items-center' : ''}" style="width:${needsSideLayout ? 'auto' : (this.settings?.thumb_size || 100) + 'px'};">
        ${isLeftSide ? html`
          <!-- story name left side of thumbnail -->
          <div class="flex items-center mr-2" style="max-width: 80px;">
            <div class="text-center transform duration-300 text-black px-1"
              style="font-weight: ${this.settings?.story_name_font_thickness || 400};font-family:${this.settings.font}; font-size:${this.settings.story_name_font_size || 14}px; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 3; overflow: hidden; line-height: 1.2; word-break: break-word;"
            >
              ${this.story.name}
            </div>
          </div>
        ` : null}

        <button
          aria-label="Open Story"
          class="focus:outline-none ${needsSideLayout ? '' : 'w-full'} justify-between"
          style="${needsSideLayout ? `width: ${this.settings?.thumb_size || 100}px;` : ''}"
          @mouseenter="${() => { this.playSpinAnimation = true; }}"
          @mouseleave="${() => { this.playSpinAnimation = false; }}"
        >
          ${(this.settings?.showStoryName ?? true) && this.settings?.storyNameDisplay === "0" ? html`
            <div class="text-center transform duration-300 my-1 text-black px-1 w-full"
              style="font-weight: ${this.settings?.story_name_font_thickness || 400};font-family:${this.settings.font}; font-size:${this.settings.story_name_font_size || 14}px; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 3; overflow: hidden; line-height: 1.2;"
            >
              ${this.story.name}
            </div>
          ` : null}

          <div class="${this.settings?.enabledEnlargeAndSpin ? 'hover:scale-[0.9]' : 'hover:opacity-75'} p-0.5 rounded-full relative"
            style="${this.settings?.enabledEnlargeAndSpin ? (this.playSpinAnimation ? `animation: spinTimeLine 1s infinite; ${spinBackgroundStyle}` : backgroundStyle) : backgroundStyle}"
          >
            <div class="circle-container bg-white p-1">
              <div class="w-full h-full rounded-full overflow-hidden relative">
                ${mediaContent}
                ${(this.settings?.showStoryName ?? true) && this.settings?.storyNameDisplay === "2" ? html`
                  <div class="story-name-overlay" style="font-weight: ${this.settings?.story_name_font_thickness || 400};font-family:${this.settings.font}; font-size:${this.settings.story_name_font_size || 14}px;">
                    ${this.story.name}
                  </div>
                ` : null}
                ${!hidePlayIcon ? html`
                  <div style="width:${playIconSize}px;height:${playIconSize}px;" class="absolute ${alignmentClasses} transform duration-300 group-hover:scale-125">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-full h-full text-white opacity-70" viewBox="0 0 24 24" fill="currentColor">
                      <path fill-rule="evenodd" d="M4.5 5.653c0-1.426 1.529-2.33 2.779-1.643l11.54 6.348c1.295.712 1.295 2.573 0 3.285L7.28 19.991c-1.25.687-2.779-.217-2.779-1.643V5.653z" clip-rule="evenodd" />
                    </svg>
                  </div>
                ` : null}
              </div>
            </div>
          </div>

          ${(this.settings?.showStoryName ?? true) && this.settings?.storyNameDisplay === "1" ? html`
            <div class="text-center transform duration-300 my-1 text-black px-1 w-full"
              style="font-weight: ${this.settings?.story_name_font_thickness || 400};font-size:${this.settings.story_name_font_size || 14}px; font-family:${this.settings.font}; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 3; overflow: hidden; line-height: 1.2;"
            >
              ${this.story.name}
            </div>
          ` : null}
        </button>

        ${isRightSide ? html`
          <!-- story name right side of thumbnail -->
          <div class="flex items-center ml-2" style="max-width: 80px;">
            <div class="text-center transform duration-300 text-black px-1"
              style="font-weight: ${this.settings?.story_name_font_thickness || 400};font-family:${this.settings.font}; font-size:${this.settings.story_name_font_size || 14}px; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 3; overflow: hidden; line-height: 1.2; word-break: break-word;"
            >
              ${this.story.name}
            </div>
          </div>
        ` : null}
      </div>
    `;
  }
}

if (!customElements.get('smart-story-circles')) customElements.define('smart-story-circles', SmartStoryCircles);
