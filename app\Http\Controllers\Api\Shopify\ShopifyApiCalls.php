<?php

namespace App\Http\Controllers\Api\Shopify;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Traits\ShopifyTrait;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use App\Mail\UninstallEmailShopify;
use Illuminate\Support\Facades\Mail;

class ShopifyApiCalls extends Controller
{
    use ShopifyTrait;
    //
    public function getCollectionAndVariantsWithMediaApi(Request $request)
    {
        //
        $shop = $request->input('shop');
        $collectionHandle = $request->input('collection');
        if($shop == null || $collectionHandle == null){
            $msg = [
                'message' => 'Missing Parameter',
                'status' => 'error',
            ];

            return response()->json($msg);
        }
        else{

            $user = User::where('shopify_domain', $shop)->first();
            if($user == null){
                $msg = [
                    'message' => 'Shop not found',
                    'status' => 'error',
                ];
                return response()->json($msg);
            }
            $token = $user->shopify_token;
            $shop = str_replace('.myshopify.com', '', $shop);
            $collectionWithMedia = $this->getCollectionAndVariantsWithMedia($shop, $token, $collectionHandle);
            $msg = [
                'data' => $collectionWithMedia,
                'status' => 'success',
            ];
            return response()->json($msg);
        }

    }

    public function shopify_uninstall_webhook(Request $request)
    {
        // Optionally, you can respond with a confirmation message
        $postData = $request->getContent();
        $headers = $request->headers->all();
        $webhook_response = json_decode($postData, JSON_PRETTY_PRINT);


        $response = ['message' => 'Webhook data saved successfully'];
        Log::info('Shopify', ['$postData'=>  $request->all()]);

        $shopWithDomain = $request->input('myshopify_domain');
        $user = User::where('shopify_domain', $shopWithDomain)->first();
        if ($user) {
            if($user->app_uninstalled_at == null){
                Mail::to($user->email)->send(new UninstallEmailShopify());
            }      
            $user->app_uninstalled_status = 'Uninstalled';
            $user->app_uninstalled_at = now();
            $user->save();
        }

        return response()->json($response);
    }
}
