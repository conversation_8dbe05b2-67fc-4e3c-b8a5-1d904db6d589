{"id": "1748266145-1489-1493596958", "version": 1, "type": "request", "time": 1748266145.085395, "method": "GET", "url": "http://localhost:8000", "uri": "/", "headers": {"host": ["localhost:8000"], "connection": ["keep-alive"], "cache-control": ["max-age=0"], "sec-ch-ua": ["\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\""], "sec-ch-ua-mobile": ["?0"], "sec-ch-ua-platform": ["\"Windows\""], "upgrade-insecure-requests": ["1"], "user-agent": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"], "accept": ["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"], "sec-fetch-site": ["none"], "sec-fetch-mode": ["navigate"], "sec-fetch-user": ["?1"], "sec-fetch-dest": ["document"], "accept-encoding": ["gzip, deflate, br, zstd"], "accept-language": ["en,ar;q=0.9,en-US;q=0.8"], "cookie": ["tokenTimestamp=1747392068374; ss_token=ePzLdIryR0Kaz3SE_qtsNZ_1748265039722; x-clockwork=%7B%22requestId%22%3A%221748266136-0659-498478761%22%2C%22version%22%3A%225.1.12%22%2C%22path%22%3A%22%5C%2F__clockwork%5C%2F%22%2C%22webPath%22%3A%22%5C%2Fclockwork%5C%2Fapp%22%2C%22token%22%3A%22f95e3028%22%2C%22metrics%22%3Atrue%2C%22toolbar%22%3Atrue%7D"]}, "controller": "App\\Http\\Controllers\\DashboardController@index", "getData": [], "postData": [], "requestData": "", "sessionData": [], "authenticatedUser": null, "cookies": {"tokenTimestamp": "1747392068374", "ss_token": "ePzLdIryR0Kaz3SE_qtsNZ_1748265039722", "x-clockwork": "{\"requestId\":\"1748266136-0659-498478761\",\"version\":\"5.1.12\",\"path\":\"\\/__clockwork\\/\",\"webPath\":\"\\/clockwork\\/app\",\"token\":\"f95e3028\",\"metrics\":true,\"toolbar\":true}"}, "responseTime": 1748266146.49309, "responseStatus": 500, "responseDuration": 1407.6948165893555, "memoryUsage": 8388608, "middleware": ["web", "localeSessionRedirect", "localizationRedirect", "localeViewPath", "auth:sanctum", "subscribed"], "databaseQueries": [], "databaseQueriesCount": 0, "databaseSlowQueries": 0, "databaseSelects": 0, "databaseInserts": 0, "databaseUpdates": 0, "databaseDeletes": 0, "databaseOthers": 0, "databaseDuration": 0, "cacheQueries": [], "cacheReads": 0, "cacheHits": 0, "cacheWrites": 0, "cacheDeletes": 0, "cacheTime": null, "modelsActions": [], "modelsRetrieved": [], "modelsCreated": [], "modelsUpdated": [], "modelsDeleted": [], "redisCommands": [], "queueJobs": [], "timelineData": [{"description": "Controller", "start": 1748266145.43568, "end": 1748266146.492592, "duration": 1056.9121837615967, "color": null, "data": null}], "log": [{"message": "Creation of dynamic property App\\Http\\Controllers\\DashboardController::$stats is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\app\\Http\\Controllers\\DashboardController.php on line 23", "exception": null, "context": {"__type__": "array"}, "level": "warning", "time": 1748266145.444915, "trace": [{"call": "Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\app\\Http\\Controllers\\DashboardController.php", "line": 23, "isVendor": false}, {"call": "App\\Http\\Controllers\\DashboardController->__construct()", "file": null, "line": null, "isVendor": false}, {"call": "ReflectionClass->newInstanceArgs()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917, "isVendor": true}, {"call": "Illuminate\\Container\\Container->build()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758, "isVendor": true}, {"call": "Illuminate\\Container\\Container->resolve()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 851, "isVendor": true}, {"call": "Illuminate\\Foundation\\Application->resolve()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 694, "isVendor": true}, {"call": "Illuminate\\Container\\Container->make()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 836, "isVendor": true}, {"call": "Illuminate\\Foundation\\Application->make()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 276, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->getController()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 1080, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->controllerMiddleware()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 1023, "isVendor": true}]}, {"message": "SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) (SQL: select * from `sessions` where `id` = poo6LGILx3TA3iI0x8Ri3mett0pbMZij9kIMViBc limit 1)", "exception": {"type": "Illuminate\\Database\\QueryException", "message": "SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) (SQL: select * from `sessions` where `id` = poo6LGILx3TA3iI0x8Ri3mett0pbMZij9kIMViBc limit 1)", "code": 1045, "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php", "line": 712, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php", "line": 45, "isVendor": true}, {"call": "Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php", "line": 30, "isVendor": true}, {"call": "Sentry\\Laravel\\Http\\SetRequestMiddleware->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php", "line": 19, "isVendor": true}, {"call": "Livewire\\DisableBrowserCache->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 40, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 86, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php", "line": 38, "isVendor": true}, {"call": "Fruitcake\\Cors\\HandleCors->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\fideloper\\proxy\\src\\TrustProxies.php", "line": 57, "isVendor": true}, {"call": "Fideloper\\Proxy\\TrustProxies->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustHosts.php", "line": 48, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\TrustHosts->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\itsgoingd\\clockwork\\Clockwork\\Support\\Laravel\\ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php", "line": 52, "isVendor": true}, {"call": "Sentry\\Laravel\\Tracing\\Middleware->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 142, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 111, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\public\\index.php", "line": 51, "isVendor": false}, {"call": "require_once()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\server.php", "line": 21, "isVendor": false}], "previous": {"type": "Doctrine\\DBAL\\Driver\\PDO\\Exception", "message": "SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO)", "code": 1045, "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\doctrine\\dbal\\lib\\Doctrine\\DBAL\\Driver\\PDO\\Exception.php", "line": 18, "trace": [{"call": "Doctrine\\DBAL\\Driver\\PDO\\Exception::new()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\doctrine\\dbal\\lib\\Doctrine\\DBAL\\Driver\\PDOConnection.php", "line": 44, "isVendor": true}, {"call": "Doctrine\\DBAL\\Driver\\PDOConnection->__construct()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php", "line": 67, "isVendor": true}, {"call": "Illuminate\\Database\\Connectors\\Connector->createPdoConnection()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Database\\Connectors\\Connector->createConnection()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php", "line": 24, "isVendor": true}, {"call": "Illuminate\\Database\\Connectors\\MySqlConnector->connect()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php", "line": 184, "isVendor": true}, {"call": "Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php", "line": 1064, "isVendor": true}, {"call": "call_user_func()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php", "line": 1064, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->getPdo()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php", "line": 1100, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->getReadPdo()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php", "line": 442, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->getPdoForSelect()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php", "line": 368, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php", "line": 705, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->runQueryCallback()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php", "line": 672, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php", "line": 2936, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->onceWithColumns()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php", "line": 2401, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->get()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php", "line": 294, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->first()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php", "line": 2377, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->find()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 100, "isVendor": true}, {"call": "Illuminate\\Session\\DatabaseSessionHandler->read()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 97, "isVendor": true}, {"call": "Illuminate\\Session\\Store->readFromHandler()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 87, "isVendor": true}, {"call": "Illuminate\\Session\\Store->loadSession()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 71, "isVendor": true}, {"call": "Illuminate\\Session\\Store->start()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147, "isVendor": true}, {"call": "Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php", "line": 263, "isVendor": true}, {"call": "tap()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 144, "isVendor": true}, {"call": "Illuminate\\Session\\Middleware\\StartSession->startSession()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 116, "isVendor": true}, {"call": "Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 64, "isVendor": true}, {"call": "Illuminate\\Session\\Middleware\\StartSession->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php", "line": 37, "isVendor": true}, {"call": "Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php", "line": 67, "isVendor": true}, {"call": "Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 719, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->runRouteWithinStack()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 698, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->runRoute()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 662, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->dispatchToRoute()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 651, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->dispatch()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php", "line": 45, "isVendor": true}, {"call": "Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php", "line": 30, "isVendor": true}, {"call": "Sentry\\Laravel\\Http\\SetRequestMiddleware->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php", "line": 19, "isVendor": true}, {"call": "Livewire\\DisableBrowserCache->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 40, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 86, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php", "line": 38, "isVendor": true}, {"call": "Fruitcake\\Cors\\HandleCors->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\fideloper\\proxy\\src\\TrustProxies.php", "line": 57, "isVendor": true}, {"call": "Fideloper\\Proxy\\TrustProxies->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustHosts.php", "line": 48, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\TrustHosts->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\itsgoingd\\clockwork\\Clockwork\\Support\\Laravel\\ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php", "line": 52, "isVendor": true}, {"call": "Sentry\\Laravel\\Tracing\\Middleware->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 142, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 111, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\public\\index.php", "line": 51, "isVendor": false}, {"call": "require_once()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\server.php", "line": 21, "isVendor": false}], "previous": {"type": "PDOException", "message": "SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO)", "code": 1045, "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\doctrine\\dbal\\lib\\Doctrine\\DBAL\\Driver\\PDOConnection.php", "line": 40, "trace": [{"call": "PDO->__construct()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\doctrine\\dbal\\lib\\Doctrine\\DBAL\\Driver\\PDOConnection.php", "line": 40, "isVendor": true}, {"call": "Doctrine\\DBAL\\Driver\\PDOConnection->__construct()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php", "line": 67, "isVendor": true}, {"call": "Illuminate\\Database\\Connectors\\Connector->createPdoConnection()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Database\\Connectors\\Connector->createConnection()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php", "line": 24, "isVendor": true}, {"call": "Illuminate\\Database\\Connectors\\MySqlConnector->connect()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php", "line": 184, "isVendor": true}, {"call": "Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php", "line": 1064, "isVendor": true}, {"call": "call_user_func()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php", "line": 1064, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->getPdo()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php", "line": 1100, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->getReadPdo()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php", "line": 442, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->getPdoForSelect()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php", "line": 368, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php", "line": 705, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->runQueryCallback()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php", "line": 672, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php", "line": 2936, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->onceWithColumns()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php", "line": 2401, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->get()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php", "line": 294, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->first()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php", "line": 2377, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->find()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 100, "isVendor": true}, {"call": "Illuminate\\Session\\DatabaseSessionHandler->read()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 97, "isVendor": true}, {"call": "Illuminate\\Session\\Store->readFromHandler()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 87, "isVendor": true}, {"call": "Illuminate\\Session\\Store->loadSession()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 71, "isVendor": true}, {"call": "Illuminate\\Session\\Store->start()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147, "isVendor": true}, {"call": "Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php", "line": 263, "isVendor": true}, {"call": "tap()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 144, "isVendor": true}, {"call": "Illuminate\\Session\\Middleware\\StartSession->startSession()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 116, "isVendor": true}, {"call": "Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 64, "isVendor": true}, {"call": "Illuminate\\Session\\Middleware\\StartSession->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php", "line": 37, "isVendor": true}, {"call": "Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php", "line": 67, "isVendor": true}, {"call": "Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 719, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->runRouteWithinStack()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 698, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->runRoute()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 662, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->dispatchToRoute()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 651, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->dispatch()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php", "line": 45, "isVendor": true}, {"call": "Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php", "line": 30, "isVendor": true}, {"call": "Sentry\\Laravel\\Http\\SetRequestMiddleware->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php", "line": 19, "isVendor": true}, {"call": "Livewire\\DisableBrowserCache->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 40, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 86, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php", "line": 38, "isVendor": true}, {"call": "Fruitcake\\Cors\\HandleCors->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\fideloper\\proxy\\src\\TrustProxies.php", "line": 57, "isVendor": true}, {"call": "Fideloper\\Proxy\\TrustProxies->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustHosts.php", "line": 48, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\TrustHosts->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\itsgoingd\\clockwork\\Clockwork\\Support\\Laravel\\ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php", "line": 52, "isVendor": true}, {"call": "Sentry\\Laravel\\Tracing\\Middleware->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 142, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 111, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\public\\index.php", "line": 51, "isVendor": false}, {"call": "require_once()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\server.php", "line": 21, "isVendor": false}], "previous": null}}}, "context": {"__type__": "array"}, "level": "error", "time": 1748266146.441235, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php", "line": 45, "isVendor": true}, {"call": "Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php", "line": 30, "isVendor": true}, {"call": "Sentry\\Laravel\\Http\\SetRequestMiddleware->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php", "line": 19, "isVendor": true}, {"call": "Livewire\\DisableBrowserCache->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Creoshift\\smart-stories\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}]}], "events": [], "routes": [], "notifications": [], "emailsData": [], "viewsData": [], "userData": [], "subrequests": [], "xdebug": {"profile": false}, "commandName": null, "commandArguments": [], "commandArgumentsDefaults": [], "commandOptions": [], "commandOptionsDefaults": [], "commandExitCode": null, "commandOutput": null, "jobName": null, "jobDescription": null, "jobStatus": null, "jobPayload": [], "jobQueue": null, "jobConnection": null, "jobOptions": [], "testName": null, "testStatus": null, "testStatusMessage": null, "testAsserts": [], "clientMetrics": [], "webVitals": [], "parent": null, "updateToken": "0ca370fb"}