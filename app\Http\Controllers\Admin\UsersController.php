<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Admin\CustomFilters\FilterName;
use App\Http\Controllers\Admin\CustomFilters\FilterSubscription;
use App\Jobs\CheckStoryCreationJob;
use App\Models\User;
use App\Models\StatsBars;
use App\Models\Subscription;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use Illuminate\Auth\Events\Registered;

use Carbon\Carbon;

class UsersController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $users = QueryBuilder::for(User::class)
            ->select('id', 'name', 'email', 'tags', 'total_consumption', 'free_subscription', 'app_uninstalled_status', 'shopify_domain', 'created_at') // Avoid selecting 'groups'
            ->where('admin', 0)
            ->allowedFilters([
                'id', 'name', 'email', 'app_uninstalled_status',
                AllowedFilter::custom('subscription', new FilterSubscription())
            ])
            ->orderBy('created_at', 'DESC')
            ->paginate(request('per_page', 10))
            ->appends(request()->query());


        return response(['success' => true, 'data' => $users]);
    }


    public function show(User $user)
    {
        $user->consumption = $user->consumption();

        $user->stories = $user->media_count();
        $user->groups_number = $user->groups()->count();
        $statsBar = StatsBars::where('user_id', $user->id)->first();

//        if ($statsBar !== null) {
        $user->impressions = number_format($statsBar->impressions ?? null);
        $user->total_unique_views = number_format($statsBar->total_unique_views ?? null);
        $user->view_rate = number_format($statsBar->view_rate ?? 0 * 100, 2, '.', '');
        $user->average_watch_time = $statsBar->average_time_watched ?? 0;
        $user->total_conversions = number_format($statsBar->conversion_rate_total ?? 0 * 100, 2, '.', '');
        $user->conversion_rate = number_format($statsBar->conversion_rate ?? 0 * 100, 2, '.', '');
        $user->lifetime_earnings = number_format($statsBar->total_revenue ?? 0);
//        }


        $user->consumption_limit = Null;
        $latestSubscription = $user->subscription()->latest()->first();
        if ($latestSubscription) {

            $user->current_plan = $latestSubscription->paddle_plan ?? "pending";
            $consumptionLimit = 'Not integrated yet';
            $planLimits = [
                'Free Plan' => 1000,
                'Standard Plan' => 20000,
                'Premium Plan' => 100000,
                'Enterprise Plan' => PHP_INT_MAX,
            ];
            $user->consumption_limit = $planLimits[$user->current_plan] ?? $consumptionLimit;


            $user->subscribed_at = $latestSubscription->created_at;
            $user->subscription_end = $latestSubscription->ends_at;

        }

        $user->is_active_conversion_tracking = $user->is_displayed;
        $imageLinks = [];
        $videoLinks = [];

        foreach ($user->groups as $group) {
            foreach ($group->stories as $story) {
                foreach ($story->media as $media) {
                    if ($media->type === 'image' && $media->image !== null) {
                        $imageLinks[] = $media->image;
                    } elseif ($media->type === 'video' && $media->video !== null) {
                        $videoLinks[] = $media->video_url;
                    }
                }
            }
        }

        $user->image_links = $imageLinks;
        $user->video_links = $videoLinks;


        return response(['success' => true, 'data' => $user]);
    }


    public function store(Request $request)
    {
        $data = \request()->validate([
            'name' => ['required'],
            'email' => ['required', 'email'],
            'password' => ['required', 'confirmed'],
        ]);

        $user = User::create([
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => bcrypt($data['password']),
            'admin' => 0,
            'demo_subscription' => true
        ]);

        event(new Registered($user));

        $delay = Carbon::parse($user->created_at)->addHours(48)->diffInRealSeconds(now());
        CheckStoryCreationJob::dispatch($user)->delay($delay);

        return response(['success' => true, 'data' => $user]);
    }

    public function update(Request $request, User $user)
    {
        $data = $request->validate([
            'email' => ['required', 'email'],
        ]);

        $user->email = $data['email'];
        $user->save();

        return response(['success' => true, 'data' => $user]);
    }

    public function status()
    {
        $data = \request()->validate([
            'user_id' => ['required'],
            'status' => ['required', 'in:activate,disactivate'],
        ]);

        $user = User::find($data['user_id']);
        if ($user->subscription->count() == 0) {
            if ($data['status'] == 'activate') {
                $user->demo_subscription = true;
            } else {
                $user->demo_subscription = false;
            }
            $user->save();
            return response(['success' => true, 'message' => 'Status Updated']);
        } else {
            return response(['success' => false], 405);
        }
    }

    public function destroy(User $user)
    {
        $user->delete();
        return response(['success' => true, 'message' => 'User Deleted']);
    }
}
