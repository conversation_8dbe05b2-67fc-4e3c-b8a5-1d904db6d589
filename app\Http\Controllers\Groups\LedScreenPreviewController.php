<?php

namespace App\Http\Controllers\Groups;


use Throwable;
use App\Models\User;
use App\Models\Group;
use App\Models\Story;
use App\Models\StoryMedia;
use Illuminate\Http\Request;
use App\Http\Traits\ShopifyTrait;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Models\ShopifyGroupCustomElement;

class LedScreenPreviewController extends Controller
{
    use ShopifyTrait;
    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function __invoke(Request $request, Group $group) {
        
        $templates = \App\Models\GroupTemplate::orderBy('order')->get();
        
        if(request('template'))
            $group->template = request('template');

        if($group->channel == 'shopify' && $group->type == 'autogenerated' ){
            $group_user = User::find($group->user_id);
            $main_shop = $group_user->shopify_domain;
            $shop = str_replace(".myshopify.com", "",$main_shop);
            $token = $group_user->shopify_token;
            $products = $this->getProducts($main_shop, $token);
            if($group->content){
                $random_product = isset($products) ? $products[rand(0,count($products)-1)] : NULL;
                $extra_details = [
                    'product' => $random_product['id'] ?? NULL,
                    'collection' => $group->collection_handle,
                ];
                try{
                    $products_stories = $this->getProductsFiltered($main_shop,$group->content,$extra_details);
                    if($products_stories == null) $products_stories = $products;
                }catch(Throwable $e) {
                    $products_stories = $products;
                }
            }else{
                $products_stories = $products;
            }

            $manual_stories =[];
            $counter = 0;
            $custom_elements = ShopifyGroupCustomElement::where('group_id',$group->id)->get();
            foreach($products_stories as $k=>$product){
                if($counter<10){
                    $counter++;
                    $manual_stories[$k] = new Story();
                    $manual_stories[$k]->id = $product['id'];
                    $manual_stories[$k]->group_id = $group->id;
                    $manual_stories[$k]->name = $product['title'];
                    $manual_stories[$k]->tags = [];
                    $manual_stories[$k]->thumbnail = null;
                    if(isset($product['images'][0]))
                        $manual_stories[$k]->thumbnail_resized = $product['images'][0]['src'] ?? 'https:'.$product['images'][0];
                    else
                        $manual_stories[$k]->thumbnail_resized = asset('images/placeholder/thumbnail.png');
                    $manual_stories[$k]->note = null;
                    $manual_stories[$k]->order_column = 0;
                    $manual_stories[$k]->media[0] = new StoryMedia();
                    $manual_stories[$k]->media[0]->id = $product['id'];
                    $manual_stories[$k]->media[0]->player_custom_elements = (object) $custom_elements;
                    $manual_stories[$k]->media[0]->type = 'image';
                    $manual_stories[$k]->media[0]->title_enabled = false;
                    $manual_stories[$k]->media[0]->mute_original_video = false;
                    $manual_stories[$k]->media[0]->animate_title = false;
                    $manual_stories[$k]->media[0]->style = $group->customizations['style'] ?? '';
                    $manual_stories[$k]->media[0]->animation = $group->customizations['animation'] ?? '';
                    $manual_stories[$k]->media[0]->animationFrom = $group->customizations['animationFrom'] ?? null;
                    $manual_stories[$k]->media[0]->animationTo = $group->customizations['animationTo'] ?? null;
                    $manual_stories[$k]->media[0]->animationEasing = $group->customizations['animationEasing'] ?? null;
                    $manual_stories[$k]->media[0]->animationEasingType = $group->customizations['animationEasingType'] ?? null;
                    $manual_stories[$k]->media[0]->background_color = $group->background_color;
                    $manual_stories[$k]->media[0]->actions = [];
                    if(isset($product['images'][0])){
                        $manual_stories[$k]->media[0]->image = $product['images'][0]['src'] ?? 'https:'.$product['images'][0];
                        $manual_stories[$k]->media[0]->image_url = $product['images'][0]['src'] ?? 'https:'.$product['images'][0];
                    }else{
                        $manual_stories[$k]->media[0]->image = asset('images/placeholder/thumbnail.png');
                        $manual_stories[$k]->media[0]->image_url = asset('images/placeholder/thumbnail.png');
                    }

                }

            }
            $group->setRelation('stories', $manual_stories);

        }
        
        return view('groups.ledScreenPreview', compact('group', 'templates'));
        
    }
}
