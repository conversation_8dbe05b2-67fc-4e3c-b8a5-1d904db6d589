<?php

namespace App\Http\Controllers;

use App\Models\Event;
use App\Models\Group;
use App\Models\Statistic;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class DashboardController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function __construct()
    {
        $this->stats = [
            0,0,0,0,0,0,0,0,0,0
        ];
    }
    public function index()
    {

        $date = Carbon::now();
        $user=Auth::user();

        $user->ip_address = getIp();
        $user->save();

        return view('dashboard');
    }

    public function latest_feed() {

       // $last_interactions = Event::select('events.*','groups.name as group_name')->join('groups','group_id','groups.id')->where('groups.user_id', Auth::user()->id)->where('events.type','view')->where('events.created_at', '>', Carbon::now()->subSeconds(30)->toDateTimeString())->where('events.created_at', '<', Carbon::now())->count();
        $curl = curl_init();
        $user=Auth::user();
        $postgreUrl = config('app.postgresql_url');
        $apiUrl = "{$postgreUrl}/api/eventsQuery/lastInteraction?user_id={$user->id}";
        
        $multiHandle = curl_multi_init();
        $curlHandles = [];

        curl_setopt_array($curl, array(
            CURLOPT_URL => $apiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Accept: application/json',
                'x-api-key: SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
                'Content-Type: application/json'
            ),
        ));
        curl_multi_add_handle($multiHandle, $curl);
        $curlHandles[] = $curl;

        $running = null;
        do {
            curl_multi_exec($multiHandle, $running);
        } while ($running > 0);
    
        foreach ($curlHandles as $handle) {
            $response = curl_multi_getcontent($handle);
        //Log::info('response', ['latest_feed' => json_decode($response)]);

            $last_interactions = json_decode($response)->data;
            curl_multi_remove_handle($multiHandle, $handle);
            curl_close($handle);
        }
        return $last_interactions;
        curl_multi_close($multiHandle);
    }

    public function feed(){
        //$last_five_mins_interactions = Event::select('events.*','groups.name as group_name')->join('groups','group_id','groups.id')->where('groups.user_id', Auth::user()->id)->where('events.type','view')->where('events.created_at', '>', Carbon::now()->subMinutes(5)->toDateTimeString())->where('events.created_at', '<', Carbon::now())->get();
        $curl = curl_init();
        $user=Auth::user();
        $postgreUrl = config('app.postgresql_url');
        $apiUrl = "{$postgreUrl}/api/eventsQuery/last_five_minutes?user_id={$user->id}";

        $multiHandle = curl_multi_init();
        $curlHandles = [];

        curl_setopt_array($curl, array(
            CURLOPT_URL => $apiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Accept: application/json',
                'x-api-key: SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
                'Content-Type: application/json'
            ),
        ));

        curl_multi_add_handle($multiHandle, $curl);
        $curlHandles[] = $curl;

        $running = null;
        do {
            curl_multi_exec($multiHandle, $running);
        } while ($running > 0);
    
        // Retrieve and process the responses
        foreach ($curlHandles as $handle) {
            $response = curl_multi_getcontent($handle);

            $last_five_mins_interactions  = json_decode($response,true)['data'];


            $to_date = Carbon::createFromFormat('Y-m-d H:s:i',date('Y-m-d H:s:i'));

            $test = collect($last_five_mins_interactions)->groupBy(function($event) use($to_date){
                $difference = $to_date->diff($event['created_at']);
                $time_in_seconds = $difference->format('%s');
                $time_in_minutes = $difference->format('%i');
                $time_in_seconds += $time_in_minutes * 60;

                if($time_in_seconds >= 270) {
                    $this->stats[0] += 1;
                }elseif($time_in_seconds > 240) {
                    $this->stats[1] += 1;
                }elseif($time_in_seconds > 210) {
                    $this->stats[2] += 1;
                }elseif($time_in_seconds > 180) {
                    $this->stats[3] += 1;
                }elseif($time_in_seconds > 150) {
                    $this->stats[4] += 1;
                }elseif($time_in_seconds > 120) {
                    $this->stats[5] += 1;
                }elseif($time_in_seconds > 90) {
                    $this->stats[6] += 1;
                }elseif($time_in_seconds > 60) {
                    $this->stats[7] += 1;
                }elseif($time_in_seconds > 30) {
                    $this->stats[8] += 1;
                }elseif($time_in_seconds >= 0) {
                    $this->stats[9] += 1;
                }


                return true;
            });
            $tmp = array_filter($this->stats);
            if (empty($tmp) && auth()->user()->demo_subscription == 1) {
                $this->stats = [2,3,1,2,1,0,2,3,1];
            }
        // dd($this->stats);
            curl_multi_remove_handle($multiHandle, $handle);
            curl_close($handle);
        }

        curl_multi_close($multiHandle);
        return $this->stats;
    }
}
