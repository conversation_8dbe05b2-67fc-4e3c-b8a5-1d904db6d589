<?php

namespace App\Http\Controllers\Profile;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Request as RQ;
use App\Models\Shop;
use App\Http\Traits\SallaTrait;
use Illuminate\Support\Facades\Log;

class BillingOption extends Controller
{
    //
    use SallaTrait;

    public function index(RQ $initial_request) {
        $current_plan = "";
        $user = auth()->user();
        $user_id = $user -> id;
        $user_shop = Shop::where('user_id','=',$user_id)->first();
        if($user_shop != null){
            $salla_shop_id = $user_shop->salla_shop_id;
            $zid_shop_id = $user_shop->zid_shop_id;
        }
        $show_section = '';
        $subscription_check= '';
        if($user->shopify_domain != null){
            $show_section = 'shopify';
        }
        elseif($salla_shop_id != null){
            $show_section = 'salla';
            $subscription_data = $this->get_app_subscription($user_shop->salla_shop_id, $user_shop->salla_access_token);
            Log::info('Salla', ['$subscription_data',  $subscription_data]);
            if($subscription_data != null && count($subscription_data) > 0){
                if(empty($subscription_data["data"][0])){
                    $subscription_check = 'empty';
                } else{ 
                    $current_plan = $subscription_data["data"][0]["plan_name"];
                    $subscription_check = '';
                }
               
            }
            
        }
        elseif($zid_shop_id){
            $show_section = 'zid';  
        }

        return view('profile.billing-option', compact('show_section', 'current_plan', 'subscription_check'));
  
    }
}
