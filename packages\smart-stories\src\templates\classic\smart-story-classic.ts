import { html } from "lit";
import { BaseSmartStory } from "../../base-smart-story";

class SmartStoryClassic extends BaseSmartStory {
  static styles = [BaseSmartStory.getStyles()];

  render() {

    const horizontalAlignment = this.settings?.thumbIcon_HorizontalAlignment || "2";
    const verticalAlignment = this.settings?.thumbIcon_VerticalAlignment || "2";

    const alignmentClasses = `
      ${horizontalAlignment === "0" ? "left-2" : horizontalAlignment === "1" ? "right-2":"left-1/2 transform -translate-x-1/2"}
      ${verticalAlignment === "0" ? "top-2" : verticalAlignment === "1" ? "bottom-2":"top-1/2 transform -translate-y-1/2"}
    `;

    const hidePlayIcon = this.settings?.hide_play_icon ?? false;
    const playIconSize = this.settings?.play_icon_size || 30;

    // Determine if we need side positioning for layout
    const isLeftSide = (this.settings?.showStoryName ?? true) && this.settings?.storyNameDisplay === "4";
    const isRightSide = (this.settings?.showStoryName ?? true) && this.settings?.storyNameDisplay === "3";
    const needsSideLayout = isLeftSide || isRightSide;

    return html`
      <div class="flex ${needsSideLayout ? 'items-center' : 'flex-col'} h-full ${needsSideLayout ? '' : 'justify-between'} ${needsSideLayout ? '' : 'w-32'} px-2">
        ${isLeftSide ? html`
          <!-- story name left side of thumbnail -->
          <div class="flex items-center mr-2" style="max-width: 80px;">
            <div class="text-center transform duration-300 text-black"
              style="font-weight: ${this.settings?.story_name_font_thickness || 400};font-size:${this.settings.story_name_font_size || 14 }px;font-family:${this.settings?.font}; display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:3;overflow: hidden;line-height: 1.2; word-break: break-word;"
            >
              ${this.story.name}
            </div>
          </div>
        ` : null}

        <div class="${needsSideLayout ? '' : 'flex flex-col h-full justify-between w-32'}">
          ${(this.settings?.showStoryName ?? true) && this.settings?.storyNameDisplay === "2"
            ? html`
                <!-- story name above thumbnail -->
                <div class="flex items-center flex-grow mb-1">
                  <div
                    class="justify-around text-center w-full transform duration-300 text-black"
                    style="font-weight: ${this.settings?.story_name_font_thickness || 400};font-size:${this.settings.story_name_font_size || 14 }px;font-family:${this.settings?.font}; display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:3;overflow: hidden;line-height: 1.2;"
                  >
                    ${this.story.name}
                  </div>
                </div>
              `
            : null}
        <button aria-label="Open Story" class="w-28 h-32 relative block group">
          <div class="w-full h-full">
            <img
              loading="lazy"
              class="w-full h-full object-cover"
              src="${this.story.thumbnail_resized ? this.story.thumbnail_resized : this.story.thumbnail_url ? this.story.thumbnail_url : `${this.appUrl}/images/placeholder/thumbnail.png`}"
              alt=""
              style="border-radius: ${this.settings?.corner_radius}px"
            >
            <div
              class="w-full h-full absolute bottom-0 bg-black bg-opacity-60 transform duration-300 group-hover:bg-transparent"
              style="border-radius: ${this.settings?.corner_radius}px"
            >
              ${(this.settings?.showStoryName ?? true) && this.settings?.storyNameDisplay === "0"
                ? html`
                    <!-- story name inside thumbnail -->
                    <div
                      style="font-weight: ${this.settings?.story_name_font_thickness || 400};font-size:${this.settings.story_name_font_size || 14 }px;font-family:${this.settings?.font}; display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:3;overflow: hidden;line-height: 1.2;"
                      class=" mt-3 px-1 text-white text-center transform duration-300 group-hover:opacity-0"
                    >
                      ${this.story.name}
                    </div>
                  `
                : null}
              ${!hidePlayIcon ? html`
                <div
                  class="bg-white mx-auto absolute ${alignmentClasses} transform duration-300 group-hover:scale-75"
                  style="width:${playIconSize}px;height:${playIconSize}px;border-radius: calc(${this.settings?.corner_radius}px/2)"
                >
                  <svg
                    class="w-full h-full"
                    fill="none"
                    stroke="${this.settings?.secondary_color}"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"
                    ></path>
                  </svg>
                </div>
              ` : null}
            </div>
          </div>
        </button>
          ${(this.settings?.showStoryName ?? true) && this.settings?.storyNameDisplay === "1"
            ? html`
                <!-- story name below thumbnail -->
                <div class="flex items-center flex-grow mt-1">
                  <div
                    class="px-1 justify-around text-center w-full transform duration-300 text-black"
                    style="font-weight: ${this.settings?.story_name_font_thickness || 400};font-size:${this.settings.story_name_font_size || 14 }px;font-family:${this.settings?.font}; display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:3;overflow: hidden;line-height: 1.2;"
                  >
                    ${this.story.name}
                  </div>
                </div>
              `
            : null}
        </div>

        ${isRightSide ? html`
          <!-- story name right side of thumbnail -->
          <div class="flex items-center ml-2" style="max-width: 80px;">
            <div class="text-center transform duration-300 text-black"
              style="font-weight: ${this.settings?.story_name_font_thickness || 400};font-size:${this.settings.story_name_font_size || 14 }px;font-family:${this.settings?.font}; display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:3;overflow: hidden;line-height: 1.2; word-break: break-word;"
            >
              ${this.story.name}
            </div>
          </div>
        ` : null}
      </div>
    `;
  }
}

if (!customElements.get('smart-story-classic')) customElements.define('smart-story-classic', SmartStoryClassic);
