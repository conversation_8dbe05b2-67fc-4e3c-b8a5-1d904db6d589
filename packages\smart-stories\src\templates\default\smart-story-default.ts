import { html } from "lit";
import { BaseSmartStory } from "../../base-smart-story";

class SmartStoryDefault extends BaseSmartStory {

    static styles = [BaseSmartStory.getStyles()]

    convertToRgba(hex: string, maxOpacity: number = 0.8): string {
        if (!hex) return `rgba(0, 0, 0, ${maxOpacity})`; // Default to black with max opacity if no color is set

        // Expand shorthand form (e.g., #03F) to full form (#0033FF)
        const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
        hex = hex.replace(shorthandRegex, (m, r, g, b) => r + r + g + g + b + b);

        // Match 6 or 8 digit hex
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})?$/i.exec(hex);

        if (!result) {
            return `rgba(0, 0, 0, ${maxOpacity})`; // Default to black with max opacity if parsing fails
        }

        const r = parseInt(result[1], 16);
        const g = parseInt(result[2], 16);
        const b = parseInt(result[3], 16);
        let hexAlpha = maxOpacity; // Default to max opacity

        if (result[4]) {
            hexAlpha = parseInt(result[4], 16) / 255; // Convert hex alpha to [0,1]
            hexAlpha = Math.min(hexAlpha, maxOpacity); // Cap the alpha at maxOpacity
        }

        return `rgba(${r}, ${g}, ${b}, ${hexAlpha})`;
    }

    render() {
        // Determine if we need side positioning for layout
        const isLeftSide = (this.settings?.showStoryName ?? true) && this.settings?.storyNameDisplay === "4";
        const isRightSide = (this.settings?.showStoryName ?? true) && this.settings?.storyNameDisplay === "3";
        const needsSideLayout = isLeftSide || isRightSide;

        return html`
            <div class="flex ${needsSideLayout ? 'items-center' : 'flex-col'} h-full ${needsSideLayout ? '' : 'justify-between'} ${needsSideLayout ? '' : 'w-32'} px-2">
                ${isLeftSide ? html`
                    <!-- story name left side of thumbnail -->
                    <div class="flex items-center mr-2" style="max-width: 80px;">
                        <div class="text-center transform duration-300 text-black"
                            style="font-weight: ${this.settings?.story_name_font_thickness || 400};font-size:${this.settings.story_name_font_size || 14 }px;font-family:${this.settings?.font}; display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:3;overflow: hidden;line-height:1.2; word-break: break-word;"
                        >
                            ${this.story.name}
                        </div>
                    </div>
                ` : null}

                <div class="${needsSideLayout ? '' : 'flex flex-col h-full justify-between w-32'}">
                    ${(this.settings?.showStoryName ?? true) && this.settings?.storyNameDisplay === "2"? html`
                    <div class="flex items-center flex-grow mb-2 mx-1">
                        <div class="justify-around text-center w-full transform duration-300 text-black"
                            style="font-weight: ${this.settings?.story_name_font_thickness || 400};font-size:${this.settings.story_name_font_size || 14 }px;font-family:${this.settings?.font}; display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:3;overflow: hidden;line-height:1.2;"
                        >
                            ${this.story.name}
                        </div>
                    </div>
                    `:null}
                    <button aria-label="Open Story" class="focus:outline-none w-28 h-36 relative text-center block group" >
                    <div class="w-full h-full block overflow-hidden">
                        <img class=" w-full h-full object-cover object-center"
                            alt=""
                            loading="lazy"
                            style="border-radius: ${this.settings?.corner_radius}px"
                            src="${this.story.thumbnail_resized ? this.story.thumbnail_resized : this.story.thumbnail_url ? this.story.thumbnail_url : `${this.appUrl}/images/placeholder/thumbnail.png`}"
                        />
                        <div class="w-full h-1/3 absolute bottom-0" style="background-color: ${this.convertToRgba(this.settings?.primary_color, 0.8)}; border-bottom-right-radius: ${this.settings?.corner_radius}px;border-bottom-left-radius: ${this.settings?.corner_radius}px;">
                            <div class="w-6 h-6 bg-white rounded-full mx-auto -mt-3 shadow-md transform duration-300 group-hover:-rotate-45 group-hover:scale-150">
                                <svg
                                    class="w-full h-full text-pink-700"
                                    fill="none"
                                    stroke=${this.settings?.secondary_color}
                                    viewBox="0 0 24 24"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"
                                    ></path>
                                </svg>
                            </div>
                            ${(this.settings?.showStoryName ?? true) && this.settings?.storyNameDisplay === "0"? html`
                                <div style="font-weight: ${this.settings?.story_name_font_thickness || 400};font-size:${this.settings.story_name_font_size || 14 }px;font-family:${this.settings?.font}; display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:3;overflow: hidden;line-height: 1.2;" class="mt-0.1 text-white px-1 transform duration-300 group-hover:mt-1.5" >
                                    ${this.story.name}
                                </div>
                            `:null}
                        </div>
                    </div>
                </button>
                    ${(this.settings?.showStoryName ?? true) && this.settings?.storyNameDisplay === "1"? html`
                    <div class="flex items-center flex-grow">
                        <div class="justify-around text-center w-full transform duration-300 text-black mt-2 mx-1"
                            style="font-weight: ${this.settings?.story_name_font_thickness || 400};font-size:${this.settings.story_name_font_size || 14 }px;font-family:${this.settings?.font}; display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:3;overflow: hidden;line-height:1.2;"
                        >
                        ${this.story.name}
                        </div>
                    </div>
                    `:null}
                </div>

                ${isRightSide ? html`
                    <!-- story name right side of thumbnail -->
                    <div class="flex items-center ml-2" style="max-width: 80px;">
                        <div class="text-center transform duration-300 text-black"
                            style="font-weight: ${this.settings?.story_name_font_thickness || 400};font-size:${this.settings.story_name_font_size || 14 }px;font-family:${this.settings?.font}; display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:3;overflow: hidden;line-height:1.2; word-break: break-word;"
                        >
                            ${this.story.name}
                        </div>
                    </div>
                ` : null}
            </div>`
    }
}
if (!customElements.get('smart-story-default')) customElements.define('smart-story-default', SmartStoryDefault);