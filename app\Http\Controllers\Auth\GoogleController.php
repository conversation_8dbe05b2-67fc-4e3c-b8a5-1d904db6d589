<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Mail\NewUser;
use App\Models\Subscription;
use App\Models\User;
use App\Models\Shop;
use Auth;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;
use App\Mail\WelcomeEmail;
use Socialite;

class GoogleController extends Controller
{

    public function redirectToGoogle()
    {
        return Socialite::driver('google')->redirect();
    }

    public function handleGoogleCallback()
    {
        try {
            $user = Socialite::driver('google')->user();
            $finduser = User::where('google_id', $user->id)->orWhere(function($q) use($user){
                $q->whereNull('google_id')->where('email',$user->email);
            })->first();
            if ($finduser) {
                Auth::login($finduser);
                return redirect()->intended('/');
            } else {
                //shopify shop register
                $shop = Session::get('shop');
                if($shop == null){
                    return redirect('login')->withError('No store detected. Please install the Smart Stories app from your store and follow the registration instructions.');
                }
                $access_token = Session::get('access_token');
                $newUser = User::create([
                    'name' => $user->name,
                    'email' => $user->email,
                    'google_id' => $user->id,
                    'ip_address' => getIp(),
                    'email_verified_at' => Carbon::now(),
                    'shopify_token' => $access_token,
                    'shopify_domain' => $shop
                ]);


                // \Log::info('shop session', ['shop', $shop]);             
                Shop::updateOrCreate(
                    [
                        'user_id' => $newUser->id,
                        'shopify_domain' => $shop
                    ],
                    [
                        'shopify_token' => $access_token,
                        'type' => 'shopify'
                    ]
                );
                // if (\App::environment('production')) {
                //     $admins = User::where(['admin' => 1])->get();
                //     foreach ($admins as $admin) {
                //         Mail::to($admin->email)->send(new NewUser($newUser));
                //     }

                // }

                if (\App::environment('production')) {
                    $notifies = Notify::first();

                    $emails = $notifies->emails ?? [];
//                    foreach ($emails as $email) {
//                        Mail::to($email)->send(new NewUser($newUser));
//                    }

                }

                Mail::to($newUser->email)->send(new WelcomeEmail());


                Auth::login($newUser);
                return redirect()->intended('/');
            }

        } catch (Exception $e) {
            return redirect('login')->withError('Email already registered');
        }
    }
}
