<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Admin\CustomFilters\FilterEmail;
use App\Http\Controllers\Admin\CustomFilters\FilterName;
use App\Http\Controllers\Admin\CustomFilters\CustomPaddleStatusFilter;
use App\Http\Controllers\Controller;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class SubscriptionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $users = QueryBuilder::for(Subscription::class)
            ->with('user')
            ->allowedFilters(['id','paddle_plan','quantity',
                AllowedFilter::custom('name', new FilterName()),
                AllowedFilter::custom('email', new FilterEmail()),
                AllowedFilter::custom('paddle_status', new CustomPaddleStatusFilter()),
            ])
            ->allowedSorts(['id', 'paddle_status','paddle_plan','quantity','ends_at'])
            ->orderBy('created_at','DESC')
            ->paginate(request('per_page', 10));

        return response(['success' => true, 'data' => $users]);
    }


    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show( $id)
    {
        $subscription=Subscription::with('user')->findOrFail($id);

        return response(['success' => true, 'data' => $subscription]);

    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
