<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http; 
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use App\Models\Subscription;
use App\Models\Shop;
use App\Models\User;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request as GuzzleRequest;
use Carbon\Carbon;
use App\Http\Traits\UpdateS3File;
use App\Http\Traits\ZidTrait;
use Illuminate\Support\Facades\Validator;
use App\Jobs\RenewFreeSubscription;
use App\Mail\ZidWelcomeEmail;
use Illuminate\Support\Facades\Mail;


class ZidController extends Controller
{
    //
    use ZidTrait;
    use UpdateS3File;

    public function zid_redirect()
    {
        $queries = http_build_query([
            'client_id' => config('zid.zid_client_id'),
            'redirect_uri' => config('app.url').'zid/callback',
            'response_type' => 'code'
        ]);
        return redirect('https://oauth.zid.sa' . '/oauth/authorize?' . $queries);
    }


    public function zid_auth_callback(Request $request)
    {   

        $auth_response = Http::post('https://oauth.zid.sa' . '/oauth/token', [
            'grant_type' => 'authorization_code',
            'client_id' => config('zid.zid_client_id'),
            'client_secret' => config('zid.zid_client_secret'),
            'redirect_uri' => config('app.url').'zid/callback',
            'code' => $request->code // grant code
        ]);

        $auth_response_decoded = json_decode($auth_response, JSON_PRETTY_PRINT);
        $zid_token_type = $auth_response_decoded["token_type"];
        $zid_access_token = $auth_response_decoded["access_token"];
        $zid_authorization_token = $zid_token_type.' '.$auth_response_decoded["authorization"];
        $zid_refresh_token = $auth_response_decoded["refresh_token"];
        //expire in seconds
        $zid_expires_in = $auth_response_decoded["expires_in"];
        // Get the current date and time
        $current_time = Carbon::now();
        // Add the given number of seconds to the current time
        $zid_expiry_date = $current_time->copy()->addSeconds($zid_expires_in);

        //getting shop id
        $manager_info = $this->get_manager_info($zid_access_token, $zid_authorization_token);
        $zid_shop_name = $manager_info["user"]["name"];
        $shop_id = $manager_info["user"]["store"]["id"];
        $zid_shop_email = $manager_info["user"]["email"];
        $zid_shop_phone_number = '+'.$manager_info["user"]["mobile_object"]["country_code"] . $manager_info["user"]["mobile_object"]["mobile"];
        $zid_domain = $manager_info["user"]["store"]["url"];
        Session::put('zid_shop_id',$shop_id);
        Session::put('zid_shop_email',$zid_shop_email);
        Session::put('zid_shop_phone_number',$zid_shop_phone_number);
        Session::put('zid_shop_name',$zid_shop_name);
        Session::put('zid_access_token',$zid_access_token);
        Session::put('zid_authorization_token',$zid_authorization_token);
        Session::put('zid_refresh_token',$zid_refresh_token);
        Session::put('zid_expiry_date',$zid_expiry_date);
        Session::put('zid_domain',$zid_domain);

        $user = \Auth::user();
        // Session::put('shop',$params['shop']);
        // Session::put('access_token', $access_token);
         //if user is logged in
        if ($user) {
            $shop = Shop::where("user_id", "=", $user->id)->first();
            //checking if user has a store then log him out
            // dd($shop && $shop->salla_store_id != null, $user->shopify_domain != null);
            if(($shop && $shop->type != "zid")){
                auth()->guard('web')->logout();
            }
            //if user does not have a shop yet
            elseif(!$shop){
                $shopWithIdCount = Shop::where('zid_shop_id', $shop_id)->count();
                if($shopWithIdCount == 0){
                    $user->update([
                        'app_uninstalled_status' => null,
                        'app_uninstalled_at' => null
                    ]);
                    //if the store is not taken then assign the store to the user
                    Shop::updateOrCreate(
                        [
                            'user_id' => $user->id,
                            'zid_shop_id' => $shop_id
                        ],
                        [
                            'zid_access_token' => $zid_access_token,
                            'zid_authorization_token' => $zid_authorization_token,
                            'zid_refresh_token' => $zid_refresh_token,
                            'zid_expires' => $zid_expiry_date,
                            'zid_domain' => $zid_domain,
                            'type' => "zid"
                        ]
                    );
                    Mail::to($user->email)->send(new ZidWelcomeEmail($user));
                }
            }
            elseif($shop->zid_shop_id == $shop_id){
                //if the store is correct then update the user and access_token
                $user->update([
                    'app_uninstalled_status' => null,
                    'app_uninstalled_at' => null
                ]);
                Shop::updateOrCreate(
                    [
                        'user_id' => $user->id,
                        'zid_shop_id' => $shop_id
                    ],
                    [
                        'zid_access_token' => $zid_access_token,
                        'zid_authorization_token' => $zid_authorization_token,
                        'zid_refresh_token' => $zid_refresh_token,
                        'zid_expires' => $zid_expiry_date,
                        'zid_domain' => $zid_domain,
                        'type' => "zid"
                    ]
                );
            }
            else{
              //not concerned with the current account then logout
              auth()->guard('web')->logout();
            }
            
        }

        //if no user is logged in
        if(!$user){
            //checking if current shop is in DB 
            //finding current shop user 
            $current_shop = Shop::where('zid_shop_id', $shop_id)->get();
            $isShopInDb = $current_shop->count();
            //if user found then login
            if($isShopInDb){
                 //update the token in case token is different
                 if($current_shop[0]->zid_access_token && ($current_shop[0]->zid_access_token != $zid_access_token)){
                    $current_shop[0]->update([
                        'zid_access_token' => $zid_access_token,
                        'zid_authorization_token' => $zid_authorization_token,
                        'zid_refresh_token' => $zid_refresh_token,
                        'zid_expires' => $zid_expiry_date
                    ]); 
                    
                }   
                //if in shop start the users's session
                $current_user = User::Find($current_shop[0]->user_id);
                $current_user->update([
                    'app_uninstalled_status' => null,
                    'app_uninstalled_at' => null
                ]);
                auth()->guard('web')->login($current_user);
                return redirect('/ar');
            }
            else{
                //if user not found then logout current user
                return redirect('/ar/zid');
            }
            // 
        }
        else{
            return redirect('/ar');
        };


    }
    public function index(Request $request)
    {

       $zid_shop_id =  Session::get('zid_shop_id');
       $admin_email =  Session::get('zid_shop_email');
       $zid_shop_name =Session::get('zid_shop_name');
       $zid_access_token =Session::get('zid_access_token');
       $zid_authorization_token =Session::get('zid_authorization_token');
       $zid_refresh_token =Session::get('zid_refresh_token');
       $zid_expiry_date =Session::get('zid_expiry_date');
       return view('zid.auth', compact('admin_email'));
    }

    public function zid_register(Request $request){
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|min:6',
        ]);
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }


       $zid_shop_id =  Session::get('zid_shop_id');
       $admin_email =  Session::get('zid_shop_email');
       $zid_shop_phone_number =  Session::get('zid_shop_phone_number');
       $zid_shop_name =Session::get('zid_shop_name');
       $zid_access_token =Session::get('zid_access_token');
       $zid_authorization_token =Session::get('zid_authorization_token');
       $zid_refresh_token =Session::get('zid_refresh_token');
       $zid_expiry_date =Session::get('zid_expiry_date');
       $zid_domain = Session::get('zid_domain');

        //creating user and shop
        $user = User::updateOrCreate(
            [
                'email' => $request->email,
            ],
            [
            'email' => $request->email,
            'password' => bcrypt($request->password),
            'phone_number' => $zid_shop_phone_number,
            'name' => $zid_shop_name
        ]);

        Shop::updateOrCreate(
            [
                'user_id' => $user->id,
                'zid_shop_id' => $zid_shop_id
            ],
            [
                'zid_access_token' => $zid_access_token,
                'zid_authorization_token' => $zid_authorization_token,
                'zid_refresh_token' => $zid_refresh_token,
                'zid_expires' => $zid_expiry_date,
                'zid_domain' => $zid_domain,
                'type' => "zid"
            ]
        );

        //subscription part
        // $manager_info = $this->get_manager_info($zid_access_token, $zid_authorization_token);
        // $current_subscription = $manager_info["user"]["store"]["subscription"];
        // $package_name = $current_subscription["package_name"]["en"];
        // $expired_at = $current_subscription["expired_at"];
        // $original_fractional_balance = $current_subscription["original_fractional_balance"];
        // $is_lifetime = $current_subscription["is_lifetime"];
        // if($is_lifetime){
        //     Subscription::create([
        //         'billable_id' => $user->id,
        //         'billable_type' => "App\Models\User",
        //         'name' => "default",
        //         'paddle_id' => null,
        //         'paddle_status' => "active",
        //         'paddle_plan' => 'Free Plan',
        //         'quantity' => 1,
        //         'trial_ends_at' => null,
        //         'paused_from' => null,
        //         'ends_at' => Carbon::now()->addDays(30),
        //         'created_at' => Carbon::now(),
        //         'updated_at' => Carbon::now(),
        //         'receipt_url' => "",
        //         'amount' => 0,
        //     ]);
        //     $user_id = $user->id;
        //     $user = User::find($user_id);
        //     RenewFreeSubscription::dispatch($user)->delay(Carbon::now()->addDays(30)->addMinute());
        //     $user->free_subscription = 1;
        //     $user->is_all_consumed= 0;
        //     $user->is_partially_consumed= 0;
        //     $user->total_consumption = 0;
        //     $user->save();
        //     $this->re_add_files_to_s3($user->id);
        // }else{

        //     Subscription::create([
        //         'billable_id' => $user->id,
        //         'billable_type' => "App\Models\User",
        //         'name' => "zid",
        //         'paddle_id' => null,
        //         'paddle_status' => "active",
        //         'paddle_plan' => $package_name,
        //         'quantity' => 1,
        //         'trial_ends_at' => null,
        //         'paused_from' => null,
        //         'ends_at' => $expired_at,
        //         'amount' => $original_fractional_balance

        //     ]);

        // }
        Mail::to($user->email)->send(new ZidWelcomeEmail($user));
        auth()->login($user);
        return redirect('/');


    }

    public function zid_webhook(Request $request){

       
        $postData = $request->getContent();
        $webhook_response = json_decode($postData, JSON_PRETTY_PRINT);
        $webhook_store = $webhook_response["store_id"];
        $webhook_event = $webhook_response["event_name"];
        if($webhook_event == "app.market.application.uninstall"){   
            $current_shop_data = Shop::where('zid_shop_id', $webhook_store)->first();
            if($current_shop_data != null){
                $user_id = $current_shop_data->user_id;

                //putting previous subscription to swaped
                $subscriptions = Subscription::where('billable_id','=', $user_id)
                                ->where('paddle_status', '=', "active");

                if($subscriptions->count() > 0){
                    $subscriptions->update(['paddle_status' => 'swaped']);
                }
                //updating uninstall
                $user = User::find($user_id);
                $user->app_uninstalled_status = 'Uninstalled';
                $user->	app_uninstalled_at = now();
                $user->save();
            }
        }

        if($webhook_event == "app.market.subscription.active"){
            $amount_paid = $webhook_response["amount_paid"];
            $email = $webhook_response["merchant_email"];
            $end_date = $webhook_response["end_date"];
            $plan_name = $webhook_response["plan_name"];

            
            if($plan_name == "خطة قياسية"){
                $plan_name = "Standard Plan";
            }
            else if($plan_name == "الخطة المميزة"){
                $plan_name = "Premium Plan";
            }
            else if($plan_name == "خطة المؤسسة"){
                $plan_name = "Enterprise Plan";
            }

            //check if user with mail is in db or not. If not create it.
            $user = User::where('email', $email)->first();
            if (!$user) {
                $user = User::create([
                    'email' => $email,
                    'name' => 'zid_store'
                ]);
            }
           
            $user_id = $user->id;
            $subscriptions = Subscription::where('billable_id','=', $user_id)
                                ->where('paddle_status', '=', "active");
            if($subscriptions->count() > 0){
                $subscriptions->update(['paddle_status' => 'swaped']);
            }
            $end_date = Carbon::parse($end_date)->format('Y-m-d H:i:s');
            Subscription::create([
                'billable_id' => $user_id,
                'billable_type' => "App\Models\User",
                'name' => "zid",
                'paddle_id' => null,
                'paddle_status' => "active",
                'paddle_plan' => $plan_name,
                'quantity' => 1,
                'trial_ends_at' => null,
                'paused_from' => null,
                'ends_at' => $end_date,
                'amount' => $amount_paid

            ]);
            $user->is_all_consumed= 0;
            $user->is_partially_consumed= 0;
            $user->total_consumption = 0;
            $user->save();
            // $this->re_add_files_to_s3($user->id);
        }
        // Optionally, you can respond with a confirmation message
        $response = ['message' => 'Webhook data saved successfully', 'postData' => $postData];
       
        // Return the response as JSON
        return response()->json($response);
    }
}
