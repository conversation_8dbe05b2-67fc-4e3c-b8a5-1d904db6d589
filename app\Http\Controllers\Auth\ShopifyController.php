<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Mail\NewUser;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use Laravel\Socialite\Facades\Socialite;

class ShopifyController extends Controller
{
    public function callback()
    {
        $shopifyUser = Socialite::driver('shopify')->stateless()->user();


        $user = User::where('email', $shopifyUser->getEmail())->first();

        if ($user || auth()->guest()) {
            if ($user) {
                $user->update([
                    'shopify_token' => $shopifyUser->token,
                    'shopify_domain' => $shopifyUser->user['domain'],
                ]);
            } else {
                $user =  User::create([
                    'name' => $shopifyUser->getName(),
                    'email' => $shopifyUser->getEmail(),
                    'password' => $shopifyUser->token,
                    'shopify_token' => $shopifyUser->token,
                    'shopify_domain' => $shopifyUser->user['domain'],
                ]);
                if (\App::environment('production')) {
                    $admins = User::where(['admin' => 1])->get();
                    foreach ($admins as $admin) {
                        Mail::to($admin->email)->send(new NewUser($user));
                    }

                }
            }

            auth()->login($user);
        } else {
            auth()->user()->update([
                'shopify_token' => $shopifyUser->token,
                'shopify_domain' => $shopifyUser->user['domain'],
            ]);
        }

        return redirect('/');
    }

    public function redirect()
    {
        return Socialite::driver('shopify')
            ->scopes(['read_products', 'read_product_listings'])
            ->redirect();
    }
}
