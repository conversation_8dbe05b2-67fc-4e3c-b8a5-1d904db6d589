import { html, css, render} from "lit";
import {property, state} from 'lit/decorators.js';

import "./smart-story-circles";
import "../../smart-stories";
import "./smart-stories-modal-circles";
import { Story, customSettingsJSON } from "../../types";
import { BaseSmartStories } from "../../base-smart-stories";




class SmartStoriesCircles extends BaseSmartStories {
    
    static styles =  [BaseSmartStories.getStyles(), css`
        @keyframes grow {
            70% { transform: translateY(1rem); }
        }
        .placeholder-scroll-container {
            display: flex;
            flex: 1 1 0%;
            align-items:center;
            justify-content:center;
            gap: 1%
        }
        .place-holder-container {
            background: linear-gradient(0deg, rgb(188, 188, 204) 30%, rgb(119 118 141) 40%);
            border: 1px solid;
            border-color: #e5e6e9 #dfe0e4 #d0d1d5;
            border-radius: 20%;
            box-shadow: rgb(188 188 204) 0px -55px 0px 0px inset;

            padding-bottom:17px;
            max-width: 112px;
            min-height: 144px;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: center;
            gap: 10px;

            flex: 1 1 0%;
        }

        @keyframes placeHolderShimmer {
            0% {
                background-position: -468px 0
            }
            100% {
                background-position: 468px 0
            }
        }

        .circle-placeholder {
            display: flex;
            animation-duration: 1s;
            animation-fill-mode: forwards;
            animation-iteration-count: infinite;
            animation-name: placeHolderShimmer;
            animation-timing-function: linear;
            background: #f6f7f8;
            background: linear-gradient(to right, #eeeeee 8%, #dddddd 18%, #eeeeee 33%);
            background-size: 800px 104px;
            height: 76px;
            width: 76px;
            border-width:4px;
            position: relative;
            border-radius: 100%;
        }

        .rectangle-placeholder {
            display: flex;
            animation-duration: 1s;
            animation-fill-mode: forwards;
            animation-iteration-count: infinite;
            animation-name: placeHolderShimmer;
            animation-timing-function: linear;
            background: #f6f7f8;
            background: linear-gradient(to right, #eeeeee 8%, #dddddd 18%, #eeeeee 33%);
            background-size: 800px 104px;
            height: 15px;
            width: 80%;
            position: relative;
        }
    `]

    // PROPERTIES
    @property({ type: Boolean }) isOverflown = true
    @property({ type: Boolean }) mediaLoaded = false
    @property() animateOnLoad = "false"
    @property() hideThumbnails: string = 'false'
    @property() target: string = ''
    @property() openStory: string = ''
    @property() is_preview = 'false'
    @property() custom_settings:customSettingsJSON
    @state() combined_settings:customSettingsJSON

    // METHODS
    handleOpenInWebView(index:number,storyId:string){
        if(this.target=="app"){
            this.activeStoryIndex = index
            let data = {
                target: "app",
                // preview_secret: this.preview_secret,
                secret: this.secret,
                openStory: storyId,
                hideThumbnails: "true",
            }

            // converts json object to encoded base64 url with parameters
            let dataString = JSON.stringify(data)
            let dataStringBase64 = encodeURIComponent(window.btoa(dataString))

            // open new window and append the data
            // window.open('/src/openInNewTab.html?data=' + dataStringBase64,"_blank")
            window.open(this.appUrl+'/assets/openInNewTab.html?data=' + dataStringBase64,"_blank")
        }
        else{
            this.activeStoryIndex = index
            this.showStory = true
        }
    }


    storyTemplate(story: Story, index: number) {
        let spacing = this.settings?.horizontal_thumb_spacing ?? 10;
        if(spacing < 0 && ((spacing*-1) > Math.ceil(this.settings.thumb_size/1.3)) ){
            spacing = -1*Math.ceil(this.settings?.thumb_size/1.3);
        }
    
        const marginStyle = index === 0 ? '' : `margin-left: ${spacing}px;`;    
        return this.storyIsActive(story) ? html`
            <div style="${marginStyle} flex-shrink: 0; position: relative;">
                <smart-story-circles
                    @click=${() => this.handleOpenInWebView(index, story.id)}
                    .story="${story}"
                    .thumbnailOrder=${index}
                    .settings="${this.combined_settings}"
                    .totalStories=${this.group?.stories.length}
                ></smart-story-circles>
            </div>` : null;
    }

    prev() {
        this.mediaLoaded = false
        if (this.activeStoryIndex === 0) { return }
        this.activeStoryIndex--

        // let story = this.group.stories[this.activeStoryIndex];
        // if (story) {
        //  this.incStoryViews(story);
        //  this.storyEvent("previous", story, this.activeStoryIndex.toString());
        // }
    }

    next() {
        this.mediaLoaded = false
        if (this.group.stories.length - 1 <= this.activeStoryIndex) { return }
        this.activeStoryIndex++

        // let story = this.group.stories[this.activeStoryIndex];
        // if (story) {
        //  this.incStoryViews(story);
        //  this.storyEvent("next", story, this.activeStoryIndex.toString());
        // }
    }
    firstUpdated() {
        this.combined_settings = this.combineJSON(this.custom_settings,this.settings)
        if(this.combined_settings['font']=="Automatic Website Font"){
            let computedStyle = window.getComputedStyle(document.body);
            let fontFamily = computedStyle.getPropertyValue('font-family');
            // console.log("before: ",this.combined_settings);
            this.combined_settings['font'] = fontFamily;
            
        }
        // console.log(this.combined_settings)
    }
    updated() {
        requestAnimationFrame(() => {
            var { scrollWidth = 0, clientWidth = 0 } = this.shadowRoot?.querySelector<HTMLElement>('#scroll-container') || {};
            this.isOverflown = scrollWidth > clientWidth
        })

        if(this.isOffline){
            setInterval(() => {
                // console.log(navigator.onLine)
                if (navigator.onLine) {
                    location.reload()
                    this.isOffline = false
                }
            }, 5000) /* repeat every 5 seconds */
        }

        // makes sure the right story opens in target="app" mode
        if(this.openStoryIsActive()){
            this.activeStoryIndex = this.group?.stories.findIndex(item => item.id == this.openStory)
        }
        // console.log("Group updated: ",this.group)
    }

    openStoryIsActive(){
        return this.openStory!=="" && this.group?.stories?.find(item => item.id == this.openStory)!==undefined
    }

    render() {
        if(!document.querySelector("#smart-stories-modal")){
            var elem = document.createElement('div');
            elem.id = "smart-stories-modal"
            document.body.appendChild(elem);
        }
        const modal = ()=> this.showStory ? html`
        <smart-stories-modal-circles
            .stories=${this.group.stories}
            .currentIndex = ${this.activeStoryIndex}
            .myStory=${this.group.stories[this.activeStoryIndex]}
            .secret=${this.secret}
            .mediaLoaded =${this.mediaLoaded}
            .settings="${this.combined_settings}"
            @storyEvent="${(e:CustomEvent)=>this.storyEvent(e)}"
            @close=${() => (this.showStory = false)}
            target=${this.target}
            .onFreePlan=${this.group?.user?.id >= 680 && this.group?.user?.free_subscription}
            .shopify_store=${this.group?.user?.shopify_domain}
            .store_type=${this.group?.user?.store_type}
            .salla_store=${this.group?.user?.store}
        ></smart-stories-modal-circles>` : null;

        render(modal(), document.getElementById("smart-stories-modal"))
        // console.log("%%%%%%%%%%%%%%%%%%",this.group)
        return !(this.group.show || this.is_preview=='true')? null: this.openStoryIsActive()? html`
        <smart-stories-modal-circles
            .stories=${this.group.stories}
            .currentIndex = ${this.activeStoryIndex}
            .secret=${this.secret}
            .mediaLoaded =${this.mediaLoaded}
            .settings=${this.combined_settings}
            target=${this.target}
            openedStory=${this.hideThumbnails==="false"}
            .onFreePlan=${this.group?.user?.id >= 680 && this.group?.user?.free_subscription}
            .shopify_store=${this.group?.user?.shopify_domain}
            .store_type=${this.group?.user?.store_type}
            .salla_store=${this.group?.user?.store}
        ></smart-stories-modal-circles>`
        :this.isActive() ?  this.combined_settings?.hideThumbnails ? null : html`
        ${this.combined_settings?.group_name?html`
            <div style="font-weight: ${this.combined_settings?.group_name_font_thickness || 500};color: ${this.combined_settings?.group_name_font_color || '#000'};font-size:${this.combined_settings?.group_name_font_size || 24}px;text-align:${this.combined_settings?.group_title_alignment == '1' ? 'right' : this.combined_settings?.group_title_alignment == '2' ? 'left' : 'center'};line-height:1.2; display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:1;overflow: hidden; font-family:${this.combined_settings?.font}" 
                class="items-center flex m-2 w-full justify-center">
                ${this.group.name}
            </div>
            ` : null
        }
        <div id="container" class="items-center flex w-full" style="justify-content: ${this.settings.thumb_alignment == '1' ? 'flex-end' : this.settings.thumb_alignment == '2' ? 'flex-start' : 'center'};">
            ${this.isOverflown ? html`
                <button aria-label="Previous Story" @click=${this.prevStory} class="bg-gray-300 w-6 h-6 p-0.5 flex-shrink-0 rounded-full focus:outline-none hidden sm:block mr-2">
                    <svg class="w-full h-full text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
                </button>`: html``
            }
    <div id="scroll-container" class="horiz-scroll flex overflow-x-auto">
        ${this.group?.stories.map((story, index) => this.storyTemplate(story, index))}
    </div>
            ${this.isOverflown ? html`
                <button aria-label="Next Story" @click=${this.nextStory} class="bg-gray-300 w-6 h-6 p-0.5 flex-shrink-0 rounded-full focus:outline-none hidden sm:block ml-2">
                    <svg class="w-full h-full text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>
                </button>`: html``
            }
        </div>`
        : this.hideThumbnails==="true" ?
        null : html`
            <div class="placeholder-scroll-container">
                ${Array.apply(null, Array(Math.ceil(window.innerWidth/200))).map(
                    ()=>{ return html`<div class="circle-placeholder"></div>`}
                    )
                }
            </div>`
    }
}
if (!customElements.get('smart-stories-circles')) customElements.define('smart-stories-circles', SmartStoriesCircles);
