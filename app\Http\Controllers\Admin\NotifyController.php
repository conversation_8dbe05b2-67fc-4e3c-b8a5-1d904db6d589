<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\Notify;


class NotifyController extends Controller
{
    public function index()
    {
        $notify=Notify::first();
        return response(['success' => true, 'data' => $notify]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'emails' => 'required|array'
        ]);
        
        $notify = Notify::updateOrCreate(['id'=>1],['emails'=>request()->emails]);
        return response(['success' => true, 'data' => $notify->emails]);
    }
    public function show(Notify $notify)
    {
        return response(['success' => true, 'data' => $notify->emails]);
    }
    public function destroy($id)
    {
        $notify = Notify::find($id);
        $notify->delete();

        return response(['data' => $notify], Response::HTTP_NO_CONTENT);
    }
}