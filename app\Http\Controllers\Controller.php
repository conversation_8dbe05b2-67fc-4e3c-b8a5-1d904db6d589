<?php

namespace App\Http\Controllers;

use App\Models\Subscription;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Http;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    public function welcome()
    {
        return view('welcome');
    }

    public function view_receipt()
    { 
        $subcription = Subscription::where('paddle_id', request()->paddle_id)->first();
        if (!$subcription->receipt_url) { 
            $receipt_url = $this->get_receipt_url();

            Subscription::where('paddle_id', request()->paddle_id)->update([
                'receipt_url' => $receipt_url
            ]); 
        }
     
        return $subcription->fresh()->receipt_url;
    }

    
    public function get_payment_method($subscription_id)
    { 
        $request['vendor_id'] = env('PADDLE_VENDOR_ID');
        $request['vendor_auth_code'] = env('PADDLE_VENDOR_AUTH_CODE');
        $request['subscription_id'] = $subscription_id;

        $user_url = $this->get_base_url() . '/subscription/users';

        $response = Http::post($user_url, $request);

        if ($response->getStatusCode() != 200) {
            abort($response->getStatusCode());
        }
                
        $user = $response->json()['response'][0]; 

        return $user['payment_information'] ?? [];
    }
    

  
    public function get_receipt_url()
    {
        $request['vendor_id'] = env('PADDLE_VENDOR_ID');
        $request['vendor_auth_code'] = env('PADDLE_VENDOR_AUTH_CODE');

        $transaction_url = $this->get_base_url() . '/subscription/'. request()->paddle_id . '/transactions';

        $response = Http::post($transaction_url, $request);

        if ($response->getStatusCode() != 200) {
            abort($response->getStatusCode());
        }
        $transaction = $response->json()['response'][0]; 
        return $transaction['receipt_url'];
    }

    public function get_base_url() {
        return env('PADDLE_SANDBOX') ? 'https://sandbox-vendors.paddle.com/api/2.0' : 'https://vendors.paddle.com/api/2.0';
    }
}
