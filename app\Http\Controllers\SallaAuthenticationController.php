<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SallaAuthenticationController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth.redirect')->except('logout');
    }

    public function index()
    {

        return view('salla.index');
    }

    public function auth(Request $request)
    {
        $request->validate([
            'email' => 'required'
        ]);

        $user = User::whereEmail($request->email)->first();
        
        if ($user) {
            if ($user->shops()->where(['type' => 'salla'])->count()) {
                if ($user->password != null) {
                    return view('salla.auth', ['user' => $user]);
                } else {
                    return view('salla.registration', ['user' => $user]);
                }
            } else return redirect()->back()
                ->withErrors(['email'=>__('Please Register first')])
                ->withInput();
        }
        abort(401);
    }

    public function setPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|min:6',
        ]);
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        $user = User::whereEmail($request->email)->firstOrFail();
        if ($user->shops()->where(['type' => 'salla'])->count()) {
            $user->password = bcrypt($request->password);
            $user->save();
            auth()->login($user);
            return redirect('/');
        } else
            abort(401);
    }
}
