<?php

namespace App\Http\Controllers\Admin;

use App\Models\GroupTemplate;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\GroupTemplateCategories;
use Spatie\QueryBuilder\QueryBuilder;
use Illuminate\Auth\Events\Registered;


class GroupTemplateCategoriesController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $templates = QueryBuilder::for(GroupTemplateCategories::class)
            ->allowedFilters(['id', 'description'])
            ->paginate(request('per_page', 100))
            ->appends(request()->query());

        return response(['success' => true, 'data' => $templates]);
    }


}
