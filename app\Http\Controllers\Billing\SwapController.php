<?php

namespace App\Http\Controllers\Billing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class SwapController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function __invoke(Request $request, $plan)
    {
        /**  @var \App\Models\User */
        $user = auth()->user();

        $user->subscription()->swap($plan);

        return redirect()->back();
    }
}
