<?php

namespace App\Http\Controllers\Profile;

use App\Http\Traits\UpdateS3File;
use GuzzleHttp\Client;
use App\Models\Subscription;
use GuzzleHttp\Psr7\Request;
use Illuminate\Http\Request as RQ;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Redirect;
use Carbon\Carbon;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Billing\CancelController;
use App\Jobs\RenewFreeSubscription;

class BillingController extends Controller
{
    use UpdateS3File;
    public function index(RQ $initial_request) {


        $user = auth()->user();
        $subscribers = Subscription::where('billable_id', auth()->id())->orderBy('created_at','DESC')->get();
        $is_subscribed = Subscription::where('billable_id', auth()->id())
                      ->where('ends_at', '>=', Carbon::today())
                      ->where('paddle_status', '=', "active")->get();


        if(empty($user->shopify_token) && empty($user->shopify_domain)){

            //remove comment when paddle is active

            // $subscribed_plan = '';
            // $paddle = true;
            // $paddle_id = optional(Subscription::where('billable_id', auth()->id())
            //         ->where('paddle_status', 'active')
            //         ->latest()
            //         ->first())
            //         ->paddle_id;

            // $payment_method = $paddle_id ? $this->get_payment_method($paddle_id) : [];

            // $monthlyStandard = config('paddle.plans.monthly.standard');
            // $monthlyPremium =  config('paddle.plans.monthly.premium');
            // $subscribed = $user->subscribed();

            // if ($subscribed) {
            //     $paylinkStandard = url("/billing/{$monthlyStandard}/swap");
            //     $paylinkPremium  = url("/billing/{$monthlyPremium}/swap");

            // } else {
            //     $paylinkStandard = $user->newSubscription('default', $monthlyStandard)
            //         ->returnTo(route('dashboard'))
            //         ->create();

            //     $paylinkPremium = $user->newSubscription('default', $monthlyPremium)
            //         ->returnTo(route('dashboard'))
            //         ->create();
            // }
            $reset_views = true;
            $payment_method = '';
            $subscribed_plan = '';
            $subscribed = false;
            $paddle = false;
            $monthlyStandard = 'Standard Plan';
            $monthlyPremium =  'Premium Plan';
            $monthlyEnterprise =  'Enterprise Plan';
            $paylinkStandard = url("/shopify");
            $paylinkPremium  = url("/shopify");
            $paylinkFree = url("/shopify");
            $paylinkEnterprise = url("/shopify");
            $shopify = false;
        }else{

            $reset_views = $initial_request->reset_views;
            $reset_views = $reset_views === 'true'? true: false;
            $paddle = false;
            $client = new Client();
            $payment_method = '';
            $monthlyStandard = 'Standard Plan';
            $monthlyPremium =  'Premium Plan';
            $monthlyEnterprise =  'Enterprise Plan';
            $shopify = true;
            $paylinkFree = url("/billing/shopify/subscribe/free");
            $paylinkStandard = url("/billing/shopify/subscribe/standard");
            $paylinkPremium = url("/billing/shopify/subscribe/premium");
            $paylinkEnterprise = url("/billing/shopify/subscribe/enterprise");
            $headers = [
                'X-Shopify-Access-Token' => $user->shopify_token,
                'Content-Type' => 'application/json',
                'Cookie' => 'request_method=POST'
            ];
            // dd($headers);
            $body = '{"query":"{currentAppInstallation {activeSubscriptions {status,name}}}","variables":{}}';
            // dd($body);
            $request = new Request('POST', 'https://'.$user->shopify_domain.'/admin/api/'.config('shopify.api_version').'/graphql.json', $headers, $body);
            try{
                $res = $client->sendAsync($request)->wait();
            }
            catch (\GuzzleHttp\Exception\ClientException $e) {
                return redirect('/shopify');
            }
            $data = json_decode($res->getBody());
            $subscribed_plan = '';
            if(!empty($data->data->currentAppInstallation->activeSubscriptions) && $data->data->currentAppInstallation->activeSubscriptions[0]->status == 'ACTIVE' && count($is_subscribed) != 0){
                $subscribed = true;
                if(!empty($data->data->currentAppInstallation->activeSubscriptions) && $data->data->currentAppInstallation->activeSubscriptions[0]->name == 'Standard Plan'){
                    $subscribed_plan = 'standard';
                }elseif(!empty($data->data->currentAppInstallation->activeSubscriptions) && ($data->data->currentAppInstallation->activeSubscriptions[0]->name == 'Premium Plan' || $data->data->currentAppInstallation->activeSubscriptions[0]->name == 'Premimum Plan' )){
                    $subscribed_plan = 'premium';
                }
                elseif(!empty($data->data->currentAppInstallation->activeSubscriptions) && ($data->data->currentAppInstallation->activeSubscriptions[0]->name == 'Enterprise Plan' )){
                    $subscribed_plan = 'enterprise';
                }
            }
            elseif($user->free_subscription){
                $subscribed = True;
                $subscribed_plan = 'free';
            }
            else{
                $subscribed = false;

            }
        }

        return view('profile.billing', compact('subscribers', 'payment_method', 'subscribed', 'paylinkFree', 'paylinkStandard', 'paylinkPremium', 'paylinkEnterprise', 'monthlyStandard', 'monthlyPremium', 'monthlyEnterprise','paddle','shopify','subscribed_plan', 'reset_views', 'user'));
    }

    public function show()
    {
        return redirect($this->view_receipt());
    }

    public function store($package){
        $user = auth()->user();
        $client = new Client();
        $amount = 0;
        $headers = [
            'X-Shopify-Access-Token' => $user->shopify_token,
            'Content-Type' => 'application/graphql',
        ];
        if($package == 'standard'){
            // // checking that the user didn't already take a free trial
            if(!$user->has_trial){
                $body = 'mutation { appSubscriptionCreate( name: "Standard Plan" trialDays:7 returnUrl: "'.config('app.url').'billing/verify-subscription" lineItems: [{ plan: { appRecurringPricingDetails: { price: { amount: 24.99, currencyCode: USD } } } }, ], ) { userErrors { field message } confirmationUrl appSubscription { id status } } }';
                $amount = 24.99;
            }
            else{
                $body = 'mutation { appSubscriptionCreate( name: "Standard Plan" returnUrl: "'.config('app.url').'billing/verify-subscription" lineItems: [{ plan: { appRecurringPricingDetails: { price: { amount: 24.99, currencyCode: USD } } } }, ], ) { userErrors { field message } confirmationUrl appSubscription { id status } } }';
                $amount = 24.99;
            }

        }elseif($package == 'free'){
            // Update user free trial
            $user->free_subscription = 1;
            $user->save();
            $cancelController = new CancelController();
            $request = RQ::create('/', 'GET', ['shopify' => 1]);
            $result = $cancelController($request);
            $subscriptions = Subscription::where('billable_id','=',auth()->id())
                           ->where('paddle_status', '=', "active");
            if($subscriptions->count() > 0){
                $subscriptions->update(['paddle_status' => 'swaped']);
            }
            Subscription::create([
                'billable_id' => $user->id,
                'billable_type' => "App\Models\User",
                'name' => "default",
                'paddle_id' => null,
                'paddle_status' => "active",
                'paddle_plan' => 'Free Plan',
                'quantity' => 1,
                'trial_ends_at' => null,
                'paused_from' => null,
                'ends_at' => Carbon::now()->addDays(30),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
                'receipt_url' => "",
                'amount' => 0,
            ]);
            RenewFreeSubscription::dispatch(auth()->user())->delay(Carbon::now()->addDays(30)->addMinute());
            return Redirect::to('/');
        }
        elseif($package == 'premium'){
            if(!$user->has_trial){
                $body = 'mutation { appSubscriptionCreate( name: "Premium Plan" trialDays:7 returnUrl: "'.config('app.url').'billing/verify-subscription" lineItems: [{ plan: { appRecurringPricingDetails: { price: { amount: 49.99, currencyCode: USD } } } }, ], ) { userErrors { field message } confirmationUrl appSubscription { id status } } }';
                $amount = 49.99;
            }
            else{
                $body = 'mutation { appSubscriptionCreate( name: "Premium Plan" returnUrl: "'.config('app.url').'billing/verify-subscription" lineItems: [{ plan: { appRecurringPricingDetails: { price: { amount: 49.99, currencyCode: USD } } } }, ], ) { userErrors { field message } confirmationUrl appSubscription { id status } } }';
                $amount = 49.99;
            }
        }

        elseif($package == 'enterprise'){    
            if(!$user->has_trial){
                $body = 'mutation { appSubscriptionCreate( name: "Enterprise Plan" trialDays:7 returnUrl: "'.config('app.url').'billing/verify-subscription" lineItems: [{ plan: { appRecurringPricingDetails: { price: { amount: 99.99, currencyCode: USD } } } }, ], ) { userErrors { field message } confirmationUrl appSubscription { id status } } }';
                $amount = 99.99;
            }else{
                $body = 'mutation { appSubscriptionCreate( name: "Enterprise Plan"  returnUrl: "'.config('app.url').'billing/verify-subscription" lineItems: [{ plan: { appRecurringPricingDetails: { price: { amount: 99.99, currencyCode: USD } } } }, ], ) { userErrors { field message } confirmationUrl appSubscription { id status } } }';
                $amount = 99.99;
            }      
            
        }
        $request = new Request('POST', 'https://'.$user->shopify_domain.'/admin/api/'.config('shopify.api_version').'/graphql.json', $headers, $body);
        $res = $client->sendAsync($request)->wait();
        // $hasSubscription = Subscription::where('billable_id','=',auth()->id())->count() > 0;


        $data = json_decode($res->getBody());
        return Redirect::to($data->data->appSubscriptionCreate->confirmationUrl);
    }

    public function verify_subscription()
    {
        $user = auth()->user();
        if(!$user->has_trial){
            $user->has_trial = 1;
            $user->save();
        }
        $client = new Client();
        $headers = [
            'X-Shopify-Access-Token' => $user->shopify_token,
            'Content-Type' => 'application/json',
            'Cookie' => 'request_method=POST'
        ];
        $body = '{"query":"{currentAppInstallation {activeSubscriptions {id, status,name, createdAt, currentPeriodEnd, trialDays  }     }}","variables":{}}';
        $request = new Request('POST', 'https://'.$user->shopify_domain.'/admin/api/'.config('shopify.api_version').'/graphql.json', $headers, $body);
        try{
                $res = $client->sendAsync($request)->wait();
        }
        catch (\GuzzleHttp\Exception\ClientException $e) {
                return redirect('/shopify');
        }
        $data = json_decode($res->getBody());
        $package = $data->data->currentAppInstallation->activeSubscriptions[0]->name;
        if($package == 'Standard Plan'){
            $amount = 24.99;
        }elseif($package == 'Enterprise Plan'){
            $amount = 99.99;
        }
        else{
            $amount = 49.99;
        }
        $dateShopify = strtotime($data->data->currentAppInstallation->activeSubscriptions[0]->createdAt);
        $subscriptionId= $data->data->currentAppInstallation->activeSubscriptions[0]->id;
        $dateShopify = date('Y-m-d', $dateShopify);

        $currentPeriodEndShopify = date('Y-m-d H:i:s',strtotime($data->data->currentAppInstallation->activeSubscriptions[0]->currentPeriodEnd));
        // dd($data, $data->data->currentAppInstallation, $currentPeriodEndShopify);
        if(!empty($data->data->currentAppInstallation->activeSubscriptions) && $data->data->currentAppInstallation->activeSubscriptions[0]->status == 'ACTIVE' && Carbon::now()->toDateString() == $dateShopify){
            $subscriptions = Subscription::where('billable_id','=',auth()->id())
                           ->where('paddle_status', '=', "active");
            if($subscriptions->count() > 0){
                $subscriptions->update(['paddle_status' => 'swaped']);
            }
                //change free subscription to 0
                $user->free_subscription = 0;
                $user->save();
                //create subscription
                if($data->data->currentAppInstallation->activeSubscriptions[0]->trialDays != 0){
                    $trial_days = $data->data->currentAppInstallation->activeSubscriptions[0]->trialDays;
                    Subscription::create([
                        'billable_id' => $user->id,
                        'billable_type' => "App\Models\User",
                        'name' => "default",
                        'paddle_id' => $subscriptionId,
                        'paddle_status' => "active",
                        'paddle_plan' => $package,
                        'quantity' => 1,
                        'trial_ends_at' => Carbon::now()->addDays($trial_days),
                        'paused_from' => null,
                        'ends_at' => Carbon::now()->addDays(30 + $trial_days),
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                        'receipt_url' => "",
                        'amount' => $amount,
                    ]);
                }
                else{
                    Subscription::create([
                        'billable_id' => $user->id,
                        'billable_type' => "App\Models\User",
                        'name' => "default",
                        'paddle_id' => $subscriptionId,
                        'paddle_status' => "active",
                        'paddle_plan' => $package,
                        'quantity' => 1,
                        'trial_ends_at' => null,
                        'paused_from' => null,
                        'ends_at' => Carbon::now()->addDays(30),
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                        'receipt_url' => "",
                        'amount' => $amount,
                    ]);
                }
                

                $this->re_add_files_to_s3($user->id);


        }
        return redirect('/?shop='.$user->shopify_domain);
    }

    public function view_consumption()
    {
        return view('profile.consumption-page');
    }

}
