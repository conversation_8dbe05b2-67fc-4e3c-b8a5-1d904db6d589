<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class UserTagController extends Controller
{
    public function index(User $user)
    {

        return response(['success' => true, 'data' => $user->tags]);

    }

    public function update(Request $request,User $user)
    {
\Log::info($request->all());
        $data = $request->validate([
            'tags' => ['array'],
        ]);

        $user->update(['tags' => $data['tags']]);

        return response(['success' => true, 'data' => $user]);

    }
}
