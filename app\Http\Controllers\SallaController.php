<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use App\Models\Subscription;
use App\Models\Shop;
use App\Models\User;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request as GuzzleRequest;
use Carbon\Carbon;
use App\Http\Traits\SallaTrait;
use App\Mail\SallaEmail;
use Illuminate\Support\Facades\Mail;
use App\Http\Traits\UpdateS3File;
use App\Jobs\RenewFreeSubscription;

class SallaController extends Controller
{
    use SallaTrait;
    use UpdateS3File;
    public function salla_webhook(Request $request){

        // Access the raw POST data from the $request object
        
        $postData = $request->getContent();
        $headers = $request->headers->all();
        $webhook_response = json_decode($postData, JSON_PRETTY_PRINT);
        $webhook_merchant = $webhook_response["merchant"];
        $webhook_event = $webhook_response["event"];
        $webhook_data =  $webhook_response["data"];
         //checking if it is the correct app
        if($webhook_data["id"] == config("salla.salla_id") && $headers["authorization"][0] == config("salla.salla_webhook_authorization")){
            //for app install
            if($webhook_event == "app.store.authorize"){
                $salla_shop = $webhook_merchant;
                $access_token = 'Bearer '.$webhook_data["access_token"];
                $refresh_token = $webhook_data["refresh_token"];
                $expiresInSeconds = $webhook_data["expires"];
                $formattedExpiresAt = Carbon::createFromTimestamp($expiresInSeconds);

                //requesting user information
                $user_info_response = $this->get_user_info($salla_shop, $access_token, false);
                $user_data = $user_info_response["data"];
                $user_email = $user_data["email"];
                $user_name = $user_data["name"];
                $user_phone_number = $user_data["mobile"];
                $salla_domain = $user_data["merchant"]["domain"];
                $user_id = -9999;

                //checking if store is already in db
                $shop = Shop::where("salla_shop_id", "=", $salla_shop)->first();
                //shop not found then start sessions and create account process;
                if(!$shop){
                    $user = User::create([
                        'name' => $user_name,
                        'email' => $user_email,
                        'phone_number' => $user_phone_number
                        // 'email' => '<EMAIL>'
                    ]);
    
                    Shop::updateOrCreate(
                        [
                            'user_id' => $user->id,
                            'salla_shop_id' => $salla_shop
                        ],
                        [
                            'salla_access_token' => $access_token,
                            'salla_refresh_token' => $refresh_token,
                            'salla_expires' => $formattedExpiresAt,
                            'salla_domain' => $salla_domain,
                            'type' => 'salla'
                        ]
                    );
                    $user_id = $user->id;
                   // Mail::to("<EMAIL>")->send(new SallaEmail($user));
                    Mail::to($user_email)->send(new SallaEmail($user));
                }
                else{
                        // Update access_token and refresh_token attributes
                        $shop->salla_access_token = $access_token;
                        $shop->salla_refresh_token = $refresh_token;
                        $shop->salla_expires = $formattedExpiresAt;
                        $user_id = $shop->user_id;
                        // Save changes to the database
                        $shop->save();
                        $user = User::find($user_id);
                        $user->app_uninstalled_status = null;
                        $user->	app_uninstalled_at = null;
                        $user->save();
                        
                }


                //checking app subscription
                $subscriptions = $this->get_app_subscription($salla_shop, $access_token);
                //getting user info subscription
                $hasSubscription = Subscription::where('billable_id','=', $user_id)
                           ->where('ends_at', '>=', Carbon::now())
                           ->where('paddle_status', '=', "active")->count() > 0;
                if(!empty($subscriptions["data"])){
                    $webhook_data = $subscriptions["data"][0];
                    if($webhook_data != null && count($webhook_data) > 0){
                        //putting previous subscription to swaped
                        $subscriptions_local = Subscription::where('billable_id','=', $user_id)
                        ->where('paddle_status', '=', "active");
    
                        if($subscriptions_local->count() > 0){
                            $subscriptions_local->update(['paddle_status' => 'swaped']);
                        }
                        
                        if( $webhook_data["end_date"] == null){
                            Subscription::create([
                                'billable_id' => $user_id,
                                'billable_type' => "App\Models\User",
                                'name' => "default",
                                'paddle_id' => null,
                                'paddle_status' => "active",
                                'paddle_plan' => 'Free Plan',
                                'quantity' => 1,
                                'trial_ends_at' => null,
                                'paused_from' => null,
                                'ends_at' => Carbon::now()->addDays(30),
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now(),
                                'receipt_url' => "",
                                'amount' => 0,
                            ]);
                            $current_shop_data = Shop::where('salla_shop_id', $webhook_merchant)->first();
                            $user_id = $current_shop_data->user_id;
                            $user = User::find($user_id);
                            RenewFreeSubscription::dispatch($user)->delay(Carbon::now()->addDays(30)->addMinute());
                            $user->free_subscription = 1;
                            $user->is_all_consumed= 0;
                            $user->is_partially_consumed= 0;
                            $user->total_consumption = 0;
                            $user->save();
                            $this->re_add_files_to_s3($user->id);
                        }
                        else{
                            $carbonEndDate = Carbon::parse($webhook_data["end_date"]);
                            $formattedEndDate = $carbonEndDate->format('Y-m-d H:i:s');
                            Subscription::create([
                                'billable_id' => $user_id,
                                'billable_type' => "App\Models\User",
                                'name' => "salla",
                                'paddle_id' => null,
                                'paddle_status' => "active",
                                'paddle_plan' => $webhook_data["plan_name"],
                                'quantity' => 1,
                                'trial_ends_at' => null,
                                'paused_from' => null,
                                'ends_at' => $formattedEndDate,
                                'amount' => $webhook_data["total"]
        
                            ]);
                            $current_shop_data = Shop::where('salla_shop_id', $webhook_merchant)->first();
                            $user_id = $current_shop_data->user_id;
                            $user = User::find($user_id);
                            $user->is_all_consumed= 0;
                            $user->is_partially_consumed= 0;
                            $user->total_consumption = 0;
                            $user->save();
                            $this->re_add_files_to_s3($user->id);
                        }
                        
                    }
                }
                

                
                // Log::info('Salla', ['salla_shop',  $salla_shop]);
                // Log::info('Salla', ['access_token',  $access_token]);
                // Log::info('Salla', ['refresh_token',  $refresh_token]);
                // Log::info('Salla', ['formattedExpiresAt',  $formattedExpiresAt]);
                // Log::info('Salla', ['user_info_response',  $user_info_response]);
                // Log::info('Salla', ['user_info_response',  $user_info_response]);
            }
            //for app subscription
            elseif($webhook_event == "app.subscription.started" || $webhook_event == "app.subscription.renewed"){        
                // getting user id
                $current_shop_data = Shop::where('salla_shop_id', $webhook_merchant)->first();
                $user_id = $current_shop_data->user_id;
                //putting previous subscription to swaped
                $subscriptions = Subscription::where('billable_id','=', $user_id)
                                  ->where('paddle_status', '=', "active");

                if($subscriptions->count() > 0){
                    $subscriptions->update(['paddle_status' => 'swaped']);
                }
                //creating the subscription with the specific shop id
                if( $webhook_data["end_date"] == null){
                    Subscription::create([
                        'billable_id' => $user_id,
                        'billable_type' => "App\Models\User",
                        'name' => "default",
                        'paddle_id' => null,
                        'paddle_status' => "active",
                        'paddle_plan' => 'Free Plan',
                        'quantity' => 1,
                        'trial_ends_at' => null,
                        'paused_from' => null,
                        'ends_at' => Carbon::now()->addDays(30),
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                        'receipt_url' => "",
                        'amount' => 0,
                    ]);
                    $user_id = $current_shop_data->user_id;
                    $user = User::find($user_id);
                    RenewFreeSubscription::dispatch($user)->delay(Carbon::now()->addDays(30)->addMinute());
                    $user->free_subscription = 1;
                    $user->is_all_consumed= 0;
                    $user->is_partially_consumed= 0;
                    $user->total_consumption = 0;
                    $user->save();
                    $this->re_add_files_to_s3($user->id);
                }
                else{
                     // Formatting date to the correct format
                    $carbonEndDate = Carbon::parse($webhook_data["end_date"]);
                    $formattedEndDate = $carbonEndDate->format('Y-m-d H:i:s');
                    Subscription::create([
                        'billable_id' => $user_id,
                        'billable_type' => "App\Models\User",
                        'name' => "salla",
                        'paddle_id' => null,
                        'paddle_status' => "active",
                        'paddle_plan' => $webhook_data["plan_name"],
                        'quantity' => 1,
                        'trial_ends_at' => null,
                        'paused_from' => null,
                        'ends_at' => $formattedEndDate,
                        'amount' => $webhook_data["total"]
    
                    ]);
                    $current_shop_data = Shop::where('salla_shop_id', $webhook_merchant)->first();
                    $user_id = $current_shop_data->user_id;
                    $user = User::find($user_id);
                    $user->is_all_consumed= 0;
                    $user->is_partially_consumed= 0;
                    $user->total_consumption = 0;
                    $user->save();
                    $this->re_add_files_to_s3($user->id);
                }
            }
            elseif($webhook_event == "app.uninstalled" || $webhook_event == "app.subscription.expired" || $webhook_event == "app.subscription.canceled"){
                // getting user id
                $current_shop_data = Shop::where('salla_shop_id', $webhook_merchant)->first();
                if($current_shop_data != null){
                    $user_id = $current_shop_data->user_id;

                    //putting previous subscription to swaped
                    $subscriptions = Subscription::where('billable_id','=', $user_id)
                                    ->where('paddle_status', '=', "active");

                    if($subscriptions->count() > 0){
                        $subscriptions->update(['paddle_status' => 'swaped']);
                    }
                    //updating uninstall
                    $user = User::find($user_id);
                    $user->app_uninstalled_status = 'Uninstalled';
                    $user->	app_uninstalled_at = now();
                    $user->save();
                }
                
            }
            elseif($webhook_event == "app.settings.updated" ){
                $salla_shop = $webhook_merchant;
                $shop = Shop::where("salla_shop_id", "=", $salla_shop)->first();
                $user_id = $shop->user_id;
                $access_token = $shop->salla_access_token;
               
                $hasSubscription = Subscription::where('billable_id','=', $user_id)
                           ->where('ends_at', '>=', Carbon::now())
                           ->where('paddle_status', '=', "active")->count() > 0;
                if(!$hasSubscription){
                    $subscriptions = $this->get_app_subscription($salla_shop, $access_token);
                    $webhook_data = $subscriptions["data"][0];
                    if($webhook_data != null && count($webhook_data) > 0){
                        //putting previous subscription to swaped
    
                        $subscriptions_local = Subscription::where('billable_id','=', $user_id)
                        ->where('paddle_status', '=', "active");
    
                        if($subscriptions_local->count() > 0){
                            $subscriptions_local->update(['paddle_status' => 'swaped']);
                        }
                        if( $webhook_data["end_date"] == null){
                            Subscription::create([
                                'billable_id' => $user_id,
                                'billable_type' => "App\Models\User",
                                'name' => "default",
                                'paddle_id' => null,
                                'paddle_status' => "active",
                                'paddle_plan' => 'Free Plan',
                                'quantity' => 1,
                                'trial_ends_at' => null,
                                'paused_from' => null,
                                'ends_at' => Carbon::now()->addDays(30),
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now(),
                                'receipt_url' => "",
                                'amount' => 0,
                            ]);
                            $current_shop_data = Shop::where('salla_shop_id', $webhook_merchant)->first();
                            $user_id = $current_shop_data->user_id;
                            $user = User::find($user_id);
                            RenewFreeSubscription::dispatch($user)->delay(Carbon::now()->addDays(30)->addMinute());
                            $user->free_subscription = 1;
                            $user->is_all_consumed= 0;
                            $user->is_partially_consumed= 0;
                            $user->total_consumption = 0;
                            $user->save();
                            $this->re_add_files_to_s3($user->id);
                        }
                        else{
                            $carbonEndDate = Carbon::parse($webhook_data["end_date"]);
                            $formattedEndDate = $carbonEndDate->format('Y-m-d H:i:s');
                            Subscription::create([
                                'billable_id' => $user_id,
                                'billable_type' => "App\Models\User",
                                'name' => "salla",
                                'paddle_id' => null,
                                'paddle_status' => "active",
                                'paddle_plan' => $webhook_data["plan_name"],
                                'quantity' => 1,
                                'trial_ends_at' => null,
                                'paused_from' => null,
                                'ends_at' => $formattedEndDate,
                                'amount' => $webhook_data["total"]
        
                            ]);
                            $current_shop_data = Shop::where('salla_shop_id', $webhook_merchant)->first();
                            $user_id = $current_shop_data->user_id;
                            $user = User::find($user_id);
                            $user->is_all_consumed= 0;
                            $user->is_partially_consumed= 0;
                            $user->total_consumption = 0;
                            $user->save();
                            $this->re_add_files_to_s3($user->id);
                        }
                        
                    }
                    
                }
                
            }
        }
        // Optionally, you can respond with a confirmation message
        $response = ['message' => 'Webhook data saved successfully', 'postData' => $postData];

        // Return the response as JSON
        return response()->json($response);
    }


    //not used anymore since it was used for custom auth mode
    // public function auth_callback(Request $request){
    //     //Log::info('Salla', ['salla_auth',  $request]);
    //     $code = request('code');
    //     $scope = request('scope');
    //     $data = [
    //         'grant_type' => 'authorization_code',
    //         'client_id' => 'a761a5de-6210-46ea-9ea2-61176e63c948',
    //         'client_secret' => '12adc11ad844c8c7a7439ae504cfd78a',
    //         'code' => $code,
    //         'scope' => 'offline_access',
    //         'redirect_uri' => config('app.url').'salla/auth',
    //     ];
    //     // Initialize cURL session
    //     $ch = curl_init("https://accounts.salla.sa/oauth2/token");

    //     // Set cURL options
    //     curl_setopt($ch, CURLOPT_POST, 1);
    //     curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));

    //     // Return the response as a string instead of outputting it
    //     curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    //     // Execute the cURL session and capture the response
    //     $response = curl_exec($ch);

    //     // Check for cURL errors
    //     if (curl_errno($ch)) {
    //         echo 'cURL Error: ' . curl_error($ch);
    //     }

    //     // Close the cURL session
    //     curl_close($ch);
    //     $json_response = json_decode($response, JSON_PRETTY_PRINT);
    //     $access_token = $json_response["access_token"];
    //     $refresh_token = $json_response["refresh_token"];
    //     // Turning expires into a date
    //     $expiresInSeconds = $json_response["expires_in"];
    //     $currentDateTime = Carbon::now();
    //     $expiresAt = $currentDateTime->addSeconds($expiresInSeconds);
    //     $formattedExpiresAt = $expiresAt->format('Y-m-d H:i:s');

    //     //getting shop info
    //     $json_shop = $this->get_salla_shop_info($access_token);
    //     // Output the response
    //     //if user is logged in log him out
    //     if(auth()->user()){
    //         auth()->guard('web')->logout();
    //     }
    //     $shop = Shop::where("salla_shop_id", "=", $json_shop["data"]["id"])->first();
    //     //shop not found then start sessions and create account process;
    //     if(!$shop){
    //         Session::put('salla_shop', $json_shop["data"]["id"]);
    //         Session::put('salla_access_token', 'Bearer '.$access_token);
    //         Session::put('salla_refresh_token', $refresh_token);
    //         Session::put('salla_expires', $formattedExpiresAt);
    //         Log::info('Salla', ['salla_auth_response',  $json_response]);
    //         Log::info('Salla', ['salla_shop',  $json_shop["data"]["id"]]);
    //         Log::info('Salla', ['salla_access_token',  'Bearer '.$access_token]);
    //         Log::info('Salla', ['refresh_token',  $refresh_token]);
    //         Log::info('Salla', ['expires_in',  $formattedExpiresAt]);
    //         return Redirect::to(config('app.url').'register');
    //     }
    //     //if shop already exists then update shop and login user
    //     else{
    //         $user = User::find($shop->user_id);
    //         //update shop
    //         $shop->update([
    //             'salla_access_token' => 'Bearer '.$access_token,
    //             'salla_refresh_token' => $refresh_token,
    //             'salla_expires' => $formattedExpiresAt,
    //             // Add more columns and their new values as needed
    //         ]);


    //         //checking app subscription
    //         $subscriptions = $this->get_app_subscription($json_shop["data"]["id"], 'Bearer '.$access_token);
    //         $webhook_data = $subscriptions["data"][0];
    //         if($webhook_data != null && count($webhook_data) > 0){
    //             //putting previous subscription to swaped
    //             $subscriptions_local = Subscription::where('billable_id','=', $user->id)
    //             ->where('paddle_status', '=', "active");

    //             if($subscriptions_local->count() > 0){
    //                 $subscriptions_local->update(['paddle_status' => 'swaped']);
    //             }
    //             $carbonEndDate = Carbon::parse($webhook_data["end_date"]);
    //             $formattedEndDate = $carbonEndDate->format('Y-m-d H:i:s');
    //             Subscription::create([
    //                 'billable_id' => $user->id,
    //                 'billable_type' => "App\Models\User",
    //                 'name' => "salla",
    //                 'paddle_id' => null,
    //                 'paddle_status' => "active",
    //                 'paddle_plan' => $webhook_data["plan_name"],
    //                 'quantity' => 1,
    //                 'trial_ends_at' => null,
    //                 'paused_from' => null,
    //                 'ends_at' => $formattedEndDate,
    //                 'amount' => $webhook_data["total"]

    //             ]);
    //         }

            
    //         //login user
    //         auth()->guard('web')->login($user);
            
    //         return Redirect::to(config('app.url'));
    //     }
        
    // }

}
