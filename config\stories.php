<?php

return [
    'templates' => [
        "big-thumbs" =>  [
            'font' => [
                "type" => 'font',
                "title" => 'Font',
                "default" => 'Automatic Website Font'
            ],
            'group_name' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'hide_play_icon' => [
                "type" => 'checkbox',
                "title" => 'Hide Icon',
            ],
            'play_icon_size' => [
                "type" => 'slider',
                "title" => 'Icon Size',
                "min" => 30,
                "max" => 100,
                "default" => 40
            ],
            'story_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 7,
                "max" => 30,
                "default" => 14
            ],
            'story_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 400
            ],
           'group_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 17,
                "max" => 40,
                "default" => 24
            ],
            'group_name_font_color' => [
                "type" => 'color',
                "title" => 'Font Color',
                "default" => '#000'
            ],
            'group_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 500
            ],
            'autoplay_audio' => [
                "type" => 'checkbox',
                "title" => 'Enable Unmute On Click',
            ],
            'mute_easy_access' => [
                "type" => 'checkbox',
                "title" => 'Sound Button Alignment',
            ],
            'hideThumbnails' => [
                "type" => 'checkbox',
                "title" => 'Hide Stories',
            ],
            'showStoryName' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'storyNameDisplay' => [
                "type" => 'storyNameDisplay',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'Above Thumbnail',0=>'Inside Thumbnail',1=>'Below Thumbnail']
            ],
            'thumb_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'group_title_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'thumbIcon_HorizontalAlignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Horizontal Play Icon Alignment',
                "default" => 2,
                "options" => [0=>'left',2=>"center",1=>"right"]
            ],
            'thumbIcon_VerticalAlignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Vertical Play Icon Alignment',
                "default" => 2,
                "options" => [0=>'top',2=>"center",1=>"bottom"]
            ],
            'primary_color' => [
                "type" => 'color',
                "title" => 'Primary',
                "default" => '#25B0CFE6'
            ],
            'secondary_color' => [
                "type" => 'color',
                "title" => 'Secondary',
                "default" => '#63A2C7C4'
            ],
            'corner_radius' => [
                "type" => 'slider',
                "title" => 'Cover Roundness',
                "min" => 0,
                "max" => 50,
                "default" => 20
            ],
            "parent_container_padding_top" => [
                "type" => 'slider',
                "title" => 'Top Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_bottom" => [
                "type" => 'slider',
                "title" => 'Bottom Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_left" => [
                "type" => 'slider',
                "title" => 'Left Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            "parent_container_padding_right" => [
                "type" => 'slider',
                "title" => 'Right Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ]
        ],
        'circles'  =>  [
            'font' => [
                "type" => 'font',
                "title" => 'Font',
                "default" => 'Automatic Website Font'
            ],
            'group_name' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
           'group_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 17,
                "max" => 40,
                "default" => 24
            ],
            'group_name_font_color' => [
                "type" => 'color',
                "title" => 'Font Color',
                "default" => '#000'
            ],
            'group_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 500
            ],
            'story_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 7,
                "max" => 30,
                "default" => 14
            ],
            'story_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 400
            ],
            'showStoryName' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'instagram_colors' => [
                "type" => 'checkbox',
                "title" => 'Use Instagram Colors',
            ],
            'hideThumbnails' => [
                "type" => 'checkbox',
                "title" => 'Hide Stories',
            ],
            'autoplay_audio' => [
                "type" => 'checkbox',
                "title" => 'Enable Unmute On Click',
            ],
            'mute_easy_access' => [
                "type" => 'checkbox',
                "title" => 'Sound Button Alignment',
            ],
            'storyNameDisplay' => [
                "type" => 'storyNameDisplay',
                "title" => 'Alignment',
                "default" => 1,
                "options" => [0=>'Above Thumbnail',2=>'Inside Thumbnail',1=>'Below Thumbnail',3=>'Right Side',4=>'Left Side']
            ],
            'thumb_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'group_title_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'thumbIcon_HorizontalAlignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Horizontal Play Icon Alignment',
                "default" => 2,
                "options" => [0=>'left',2=>"center",1=>"right"]
            ],
            'hide_play_icon' => [
                "type" => 'checkbox',
                "title" => 'Hide Icon',
            ],
            'play_icon_size' => [
                "type" => 'slider',
                "title" => 'Icon Size',
                "min" => 30,
                "max" => 70,
                "default" => 30
            ],
            'thumbIcon_VerticalAlignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Vertical Play Icon Alignment',
                "default" => 2,
                "options" => [0=>'top',2=>"center",1=>"bottom"]
            ],
            'enabledEnlargeAndSpin' => [
                "type" => 'checkbox',
                "title" => 'Hover Effect',
            ],
            'thumbnailPlaybackMode' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Autoplay',
                "default" => 0,
                "options" => [0=>'No Autoplay',1=>"Play First Only",2=>"Play in Sequence",3=>"Autoplay All"]
            ],
            'primary_color' => [
                "type" => 'color',
                "title" => 'Primary',
                "default" => '#25B0CFE6'
            ],
            'secondary_color' => [
                "type" => 'color',
                "title" => 'Secondary',
                "default" => '#63A2C7C4'
            ],
            'thumb_size' => [
                "type" => 'slider',
                "title" => 'Cover Size',
                "min" => 50,
                "max" => 500,
                "default" => 80
            ],
            "parent_container_padding_top" => [
                "type" => 'slider',
                "title" => 'Top Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_bottom" => [
                "type" => 'slider',
                "title" => 'Bottom Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_left" => [
                "type" => 'slider',
                "title" => 'Left Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            "parent_container_padding_right" => [
                "type" => 'slider',
                "title" => 'Right Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            'horizontal_thumb_spacing' => [
                "type" => 'slider',
                "title" => 'Stories Horizontal Spacing',
                "min" => -400,
                "max" => 500,
                "default" => 10
            ]
        ],
        'sticky'  =>  [
            'font' => [
                "type" => 'font',
                "title" => 'Font',
                "default" => 'Automatic Website Font'
            ],
            'group_name' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
           'group_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 17,
                "max" => 40,
                "default" => 24
            ],
            'group_name_font_color' => [
                "type" => 'color',
                "title" => 'Font Color',
                "default" => '#000'
            ],
            'group_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 500
            ],
            'group_title_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'story_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 7,
                "max" => 30,
                "default" => 14
            ],
            'story_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 400
            ],
            'thumb_size' => [
                "type" => 'slider',
                "title" => 'Cover Size',
                "min" => 50,
                "max" => 500,
                "default" => 80
            ],
            'horizontal_thumb_spacing' => [
                "type" => 'slider',
                "title" => 'Stories Horizontal Spacing',
                "min" => -400,
                "max" => 500,
                "default" => 10
            ],
            'vertical_thumb_spacing' => [
                "type" => 'slider',
                "title" => 'Stories Vertical Spacing',
                "min" => -400,
                "max" => 500,
                "default" => 10
            ],
            'autoplay_audio' => [
                "type" => 'checkbox',
                "title" => 'Enable Unmute On Click',
            ],
            'instagram_colors' => [
                "type" => 'checkbox',
                "title" => 'Use Instagram Colors',
            ],
            'hideThumbnails' => [
                "type" => 'checkbox',
                "title" => 'Hide Stories',
            ],
            'mute_easy_access' => [
                "type" => 'checkbox',
                "title" => 'Sound Button Alignment',
            ],
            'storyNameDisplay' => [
                "type" => 'storyNameDisplay',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [0=>'Above Thumbnail',2=>'Inside Thumbnail',1=>'Below Thumbnail',3=>'Right Side',4=>'Left Side']
            ],
            'showStoryName' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'enabledEnlargeAndSpin' => [
                "type" => 'checkbox',
                "title" => 'Hover Effect',
            ],
            'thumbnailPlaybackMode' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Autoplay',
                "default" => 0,
                "options" => [0=>'No Autoplay',1=>"Play First Only",2=>"Play in Sequence",3=>"Autoplay All"]
            ],
            'thumbIcon_HorizontalAlignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Horizontal Play Icon Alignment',
                "default" => 2,
                "options" => [0=>'left',2=>"center",1=>"right"]
            ],
            'hide_play_icon' => [
                "type" => 'checkbox',
                "title" => 'Hide Icon',
            ],
            'play_icon_size' => [
                "type" => 'slider',
                "title" => 'Icon Size',
                "min" => 30,
                "max" => 70,
                "default" => 30
            ],
            'thumbIcon_VerticalAlignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Vertical Play Icon Alignment',
                "default" => 2,
                "options" => [0=>'top',2=>"center",1=>"bottom"]
            ],
            'primary_color' => [
                "type" => 'color',
                "title" => 'Primary',
                "default" => '#25B0CFE6'
            ],
            'secondary_color' => [
                "type" => 'color',
                "title" => 'Secondary',
                "default" => '#63A2C7C4'
            ],
            "parent_container_padding_top" => [
                "type" => 'slider',
                "title" => 'Top Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_bottom" => [
                "type" => 'slider',
                "title" => 'Bottom Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_left" => [
                "type" => 'slider',
                "title" => 'Left Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            "parent_container_padding_right" => [
                "type" => 'slider',
                "title" => 'Right Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            'sticky_theme' => [
                "type" => 'dropdown_options_list',
                "title" => 'Story Mode',
                "default" => 0,
                "options" => [0=>'Arrow',1=>'Close']
            ],
            'sticky_position' => [
                "type" => 'dropdown_options_list',
                "title" => 'Alignment',
                "default" => 1,
                "options" => [1=>'left',0=>'top',3=>"bottom",2=>"right"]
            ],
            'sticky_transparency' => [
                "type" => 'slider',
                "title" => 'Background Opacity',
                "min" => 0,
                "max" => 100,
                "default" => 50
            ]
        ],
        "classic" =>  [
            'font' => [
                "type" => 'font',
                "title" => 'Font',
                "default" => 'Automatic Website Font'
            ],
            'corner_radius' => [
                "type" => 'slider',
                "title" => 'Cover Roundness',
                "min" => 0,
                "max" => 50,
                "default" => 20
            ],
            'hide_play_icon' => [
                "type" => 'checkbox',
                "title" => 'Hide Icon',
            ],
            'play_icon_size' => [
                "type" => 'slider',
                "title" => 'Icon Size',
                "min" => 20,
                "max" => 70,
                "default" => 30
            ],
            'group_name' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'hideThumbnails' => [
                "type" => 'checkbox',
                "title" => 'Hide Stories',
            ],
           'group_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 17,
                "max" => 40,
                "default" => 24
            ],
            'group_name_font_color' => [
                "type" => 'color',
                "title" => 'Font Color',
                "default" => '#000'
            ],
            'group_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 500
            ],
            'group_title_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'story_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 7,
                "max" => 30,
                "default" => 14
            ],
            'story_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 400
            ],
            'autoplay_audio' => [
                "type" => 'checkbox',
                "title" => 'Enable Unmute On Click',
            ],
            'mute_easy_access' => [
                "type" => 'checkbox',
                "title" => 'Sound Button Alignment',
            ],
            'storyNameDisplay' => [
                "type" => 'storyNameDisplay',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'Above Thumbnail',0=>'Inside Thumbnail',1=>'Below Thumbnail',3=>'Right Side',4=>'Left Side']
            ],
            'showStoryName' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'thumb_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'thumbIcon_HorizontalAlignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Horizontal Play Icon Alignment',
                "default" => 2,
                "options" => [0=>'left',2=>"center",1=>"right"]
            ],
            'thumbIcon_VerticalAlignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Vertical Play Icon Alignment',
                "default" => 2,
                "options" => [0=>'top',2=>"center",1=>"bottom"]
            ],
            'primary_color' => [
                "type" => 'color',
                "title" => 'Primary',
                "default" => '#25B0CFE6'
            ],
            'secondary_color' => [
                "type" => 'color',
                "title" => 'Secondary',
                "default" => '#63A2C7C4'
            ],
            "parent_container_padding_top" => [
                "type" => 'slider',
                "title" => 'Top Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_bottom" => [
                "type" => 'slider',
                "title" => 'Bottom Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_left" => [
                "type" => 'slider',
                "title" => 'Left Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            "parent_container_padding_right" => [
                "type" => 'slider',
                "title" => 'Right Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            'horizontal_thumb_spacing' => [
                "type" => 'slider',
                "title" => 'Stories Horizontal Spacing',
                "min" => 0,
                "max" => 500,
                "default" => 10
            ]
        ],
        "default" =>  [
            'font' => [
                "type" => 'font',
                "title" => 'Font',
                "default" => 'Automatic Website Font'
            ],
            'corner_radius' => [
                "type" => 'slider',
                "title" => 'Cover Roundness',
                "min" => 0,
                "max" => 50,
                "default" => 20
            ],
            'group_name' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'hideThumbnails' => [
                "type" => 'checkbox',
                "title" => 'Hide Stories',
            ],
           'group_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 17,
                "max" => 40,
                "default" => 24
            ],
            'group_name_font_color' => [
                "type" => 'color',
                "title" => 'Font Color',
                "default" => '#000'
            ],
            'group_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 500
            ],
            'story_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 400
            ],
            'group_title_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'story_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 7,
                "max" => 30,
                "default" => 14
            ],
            'autoplay_audio' => [
                "type" => 'checkbox',
                "title" => 'Enable Unmute On Click',
            ],
            'mute_easy_access' => [
                "type" => 'checkbox',
                "title" => 'Sound Button Alignment',
            ],
            'storyNameDisplay' => [
                "type" => 'storyNameDisplay',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'Above Thumbnail',0=>'Inside Thumbnail',1=>'Below Thumbnail',3=>'Right Side',4=>'Left Side']
            ],
            'showStoryName' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'thumb_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'primary_color' => [
                "type" => 'color',
                "title" => 'Primary',
                "default" => '#25B0CFE6'
            ],
            'secondary_color' => [
                "type" => 'color',
                "title" => 'Secondary',
                "default" => '#63A2C7C4'
            ],
            "parent_container_padding_top" => [
                "type" => 'slider',
                "title" => 'Top Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_bottom" => [
                "type" => 'slider',
                "title" => 'Bottom Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_left" => [
                "type" => 'slider',
                "title" => 'Left Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            "parent_container_padding_right" => [
                "type" => 'slider',
                "title" => 'Right Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            'horizontal_thumb_spacing' => [
                "type" => 'slider',
                "title" => 'Stories Horizontal Spacing',
                "min" => 0,
                "max" => 500,
                "default" => 10
            ]
        ],
        "dynamic" =>  [
            'font' => [
                "type" => 'font',
                "title" => 'Font',
                "default" => 'Automatic Website Font'
            ],
            'storyNameDisplay' => [
                "type" => 'storyNameDisplay',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'Above Thumbnail',0=>'Inside Thumbnail',1=>'Below Thumbnail',3=>'Right Side',4=>'Left Side']
            ],
            'showStoryName' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'thumb_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'group_title_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'corner_radius' => [
                "type" => 'slider',
                "title" => 'Cover Roundness',
                "min" => 0,
                "max" => 50,
                "default" => 20
            ],
            'group_name' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'thumbIcon_HorizontalAlignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Horizontal Play Icon Alignment',
                "default" => 2,
                "options" => [0=>'left',2=>"center",1=>"right"]
            ],
            'thumbIcon_VerticalAlignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Vertical Play Icon Alignment',
                "default" => 2,
                "options" => [0=>'top',2=>"center",1=>"bottom"]
            ],
            'hide_play_icon' => [
                "type" => 'checkbox',
                "title" => 'Hide Icon',
            ],
            'play_icon_size' => [
                "type" => 'slider',
                "title" => 'Icon Size',
                "min" => 30,
                "max" => 70,
                "default" => 40
            ],
            'hideThumbnails' => [
                "type" => 'checkbox',
                "title" => 'Hide Stories',
            ],
           'group_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 17,
                "max" => 40,
                "default" => 24
            ],
            'group_name_font_color' => [
                "type" => 'color',
                "title" => 'Font Color',
                "default" => '#000'
            ],
            'group_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 500
            ],
            'story_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 400
            ],
            'story_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 7,
                "max" => 30,
                "default" => 14
            ],
            'autoplay_audio' => [
                "type" => 'checkbox',
                "title" => 'Enable Unmute On Click',
            ],
            'mute_easy_access' => [
                "type" => 'checkbox',
                "title" => 'Sound Button Alignment',
            ],
            'fill' => [
                "type" => 'checkbox',
                "title" => 'Fill Cover Background',
            ],
            'sticky_transparency' => [
                "type" => 'slider',
                "title" => 'Background Opacity',
                "min" => 0,
                "max" => 100,
                "default" => 50
            ],
            'skew_sync' => [
                "type" => 'checkbox',
                "title" => 'Hover Effect',
            ],
            'primary_color' => [
                "type" => 'color',
                "title" => 'Primary',
                "default" => '#25B0CFE6'
            ],
            'secondary_color' => [
                "type" => 'color',
                "title" => 'Secondary',
                "default" => '#63A2C7C4'
            ],
            "parent_container_padding_top" => [
                "type" => 'slider',
                "title" => 'Top Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_bottom" => [
                "type" => 'slider',
                "title" => 'Bottom Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_left" => [
                "type" => 'slider',
                "title" => 'Left Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            "parent_container_padding_right" => [
                "type" => 'slider',
                "title" => 'Right Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            'horizontal_thumb_spacing' => [
                "type" => 'slider',
                "title" => 'Stories Horizontal Spacing',
                "min" => 0,
                "max" => 500,
                "default" => 10
            ]
        ],
        "floaty" =>  [
            'font' => [
                "type" => 'font',
                "title" => 'Font',
                "default" => 'Automatic Website Font'
            ],
            'corner_radius' => [
                "type" => 'slider',
                "title" => 'Cover Roundness',
                "min" => 0,
                "max" => 50,
                "default" => 20
            ],
            'floaty_size' => [
                "type" => 'slider',
                "title" => 'Cover Size',
                "min" => 50,
                "max" => 300,
                "default" => 150
            ],
            'floaty_vertical_position' => [
                "type" => 'slider',
                "title" => 'Vertical Position',
                "min" => 10,
                "max" => 400,
                "default" => 40
            ],
            'floaty_horizontal_position' => [
                "type" => 'slider',
                "title" => 'Horizontal Position',
                "min" => 10,
                "max" => 400,
                "default" => 40
            ],
            'primary_color' => [
                "type" => 'color',
                "title" => 'Primary',
                "default" => '#25B0CFE6'
            ],
            'hideThumbnails' => [
                "type" => 'checkbox',
                "title" => 'Hide Stories',
            ],
            'floaty_theme' => [
                "type" => 'storyNameDisplay',
                "title" => 'Story Shape',
                "default" => 0,
                "options" => [0=>'Classic',1=>'Circular']
            ],
            'autoplay_audio' => [
                "type" => 'checkbox',
                "title" => 'Enable Unmute On Click',
            ],
            'mute_easy_access' => [
                "type" => 'checkbox',
                "title" => 'Sound Button Alignment',
            ],
            'floaty_corner_position' => [
                "type" => 'dropdown_options_list',
                "title" => 'Floaty Corner Alignment',
                "default" => 0,
                "options" => [0=>'Bottom Left',2=>'Upper Left',1=>'Bottom Right',3=>'Upper Right']
            ]
        ],
        "formats" =>  [
            'font' => [
                "type" => 'font',
                "title" => 'Font',
                "default" => 'Automatic Website Font'
            ],
            'corner_radius' => [
                "type" => 'slider',
                "title" => 'Cover Roundness',
                "min" => 0,
                "max" => 50,
                "default" => 10
            ],
            'group_name' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'hideThumbnails' => [
                "type" => 'checkbox',
                "title" => 'Hide Stories',
            ],
           'group_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 17,
                "max" => 40,
                "default" => 24
            ],
            'group_name_font_color' => [
                "type" => 'color',
                "title" => 'Font Color',
                "default" => '#000'
            ],
            'group_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 500
            ],
            'story_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 400
            ],
            // 'autoplay_audio' => [
            //     "type" => 'checkbox',
            //     "title" => 'Enable Unmute On Click',
            // ],
            'mute_easy_access' => [
                "type" => 'checkbox',
                "title" => 'Sound Button Alignment',
            ],
            'thumb_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'primary_color' => [
                "type" => 'color',
                "title" => 'Primary',
                "default" => '#25B0CFE6'
            ],
            'secondary_color' => [
                "type" => 'color',
                "title" => 'Secondary',
                "default" => '#63A2C7C4'
            ],
            "parent_container_padding_top" => [
                "type" => 'slider',
                "title" => 'Top Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_bottom" => [
                "type" => 'slider',
                "title" => 'Bottom Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_left" => [
                "type" => 'slider',
                "title" => 'Left Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            "parent_container_padding_right" => [
                "type" => 'slider',
                "title" => 'Right Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ]
        ],
        "grid" =>  [
            'font' => [
                "type" => 'font',
                "title" => 'Font',
                "default" => 'Automatic Website Font'
            ],
            'corner_radius' => [
                "type" => 'slider',
                "title" => 'Cover Roundness',
                "min" => 0,
                "max" => 50,
                "default" => 20
            ],
            'group_name' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'hideThumbnails' => [
                "type" => 'checkbox',
                "title" => 'Hide Stories',
            ],
            'autoplay_audio' => [
                "type" => 'checkbox',
                "title" => 'Enable Unmute On Click',
            ],
            'group_title_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
           'group_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 17,
                "max" => 40,
                "default" => 24
            ],
            'group_name_font_color' => [
                "type" => 'color',
                "title" => 'Font Color',
                "default" => '#000'
            ],
            'group_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 500
            ],
            'story_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 400
            ],
            'story_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 7,
                "max" => 30,
                "default" => 10
            ],
            'storyNameDisplay' => [
                "type" => 'storyNameDisplay',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'Above Thumbnail',0=>'Inside Thumbnail',1=>'Below Thumbnail']
            ],
            'showStoryName' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'thumb_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'mute_easy_access' => [
                "type" => 'checkbox',
                "title" => 'Sound Button Alignment',
            ],
            'primary_color' => [
                "type" => 'color',
                "title" => 'Primary',
                "default" => '#25B0CFE6'
            ],
            'secondary_color' => [
                "type" => 'color',
                "title" => 'Secondary',
                "default" => '#63A2C7C4'
            ],
            "parent_container_padding_top" => [
                "type" => 'slider',
                "title" => 'Top Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_bottom" => [
                "type" => 'slider',
                "title" => 'Bottom Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_left" => [
                "type" => 'slider',
                "title" => 'Left Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            "parent_container_padding_right" => [
                "type" => 'slider',
                "title" => 'Right Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ]
        ],
        "modern" =>  [
            'font' => [
                "type" => 'font',
                "title" => 'Font',
                "default" => 'Automatic Website Font'
            ],
            'corner_radius' => [
                "type" => 'slider',
                "title" => 'Cover Roundness',
                "min" => 0,
                "max" => 50,
                "default" => 20
            ],
            'group_name' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'hideThumbnails' => [
                "type" => 'checkbox',
                "title" => 'Hide Stories',
            ],
            'autoplay_audio' => [
                "type" => 'checkbox',
                "title" => 'Enable Unmute On Click',
            ],
           'group_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 17,
                "max" => 40,
                "default" => 24
            ],
            'group_name_font_color' => [
                "type" => 'color',
                "title" => 'Font Color',
                "default" => '#000'
            ],
            'group_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 500
            ],
            'group_title_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'story_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 400
            ],
            'story_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 7,
                "max" => 30,
                "default" => 10
            ],
            'storyNameDisplay' => [
                "type" => 'storyNameDisplay',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'Above Thumbnail',0=>'Inside Thumbnail',1=>'Below Thumbnail']
            ],
            'showStoryName' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'thumb_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'mute_easy_access' => [
                "type" => 'checkbox',
                "title" => 'Sound Button Alignment',
            ],
            'primary_color' => [
                "type" => 'color',
                "title" => 'Primary',
                "default" => '#25B0CFE6'
            ],
            'secondary_color' => [
                "type" => 'color',
                "title" => 'Secondary',
                "default" => '#FFFFFF'
            ],
            "parent_container_padding_top" => [
                "type" => 'slider',
                "title" => 'Top Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_bottom" => [
                "type" => 'slider',
                "title" => 'Bottom Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_left" => [
                "type" => 'slider',
                "title" => 'Left Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            "parent_container_padding_right" => [
                "type" => 'slider',
                "title" => 'Right Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ]
        ],
        "no-thumbs1" =>  [
            'font' => [
                "type" => 'font',
                "title" => 'Font',
                "default" => 'Automatic Website Font'
            ],
            'group_name' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'hideThumbnails' => [
                "type" => 'checkbox',
                "title" => 'Hide Stories',
            ],
           'group_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 17,
                "max" => 40,
                "default" => 24
            ],
            'group_name_font_color' => [
                "type" => 'color',
                "title" => 'Font Color',
                "default" => '#000'
            ],
            'group_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 500
            ],
            'group_title_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'thumb_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'mute_easy_access' => [
                "type" => 'checkbox',
                "title" => 'Sound Button Alignment',
            ],
            'autoplay_audio' => [
                "type" => 'checkbox',
                "title" => 'Enable Unmute On Click',
            ],
            'corner_radius' => [
                "type" => 'slider',
                "title" => 'Cover Roundness',
                "min" => 0,
                "max" => 50,
                "default" => 20
            ],
            'primary_color' => [
                "type" => 'color',
                "title" => 'Primary',
                "default" => '#25B0CFE6'
            ],
            "parent_container_padding_top" => [
                "type" => 'slider',
                "title" => 'Top Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_bottom" => [
                "type" => 'slider',
                "title" => 'Bottom Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_left" => [
                "type" => 'slider',
                "title" => 'Left Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            "parent_container_padding_right" => [
                "type" => 'slider',
                "title" => 'Right Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ]
        ],
        "open-modal1" =>  [
            'font' => [
                "type" => 'font',
                "title" => 'Font',
                "default" => 'Automatic Website Font'
            ],
            'storyNameDisplay' => [
                "type" => 'storyNameDisplay',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [1=>'Before Thumbnail',0=>'After Thumbnail']
            ],
            'showStoryName' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'autoplay_audio' => [
                "type" => 'checkbox',
                "title" => 'Enable Unmute On Click',
            ],
            'thumb_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'group_title_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'corner_radius' => [
                "type" => 'slider',
                "title" => 'Cover Roundness',
                "min" => 0,
                "max" => 50,
                "default" => 20
            ],
            'group_name' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'hideThumbnails' => [
                "type" => 'checkbox',
                "title" => 'Hide Stories',
            ],
           'group_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 17,
                "max" => 40,
                "default" => 24
            ],
            'group_name_font_color' => [
                "type" => 'color',
                "title" => 'Font Color',
                "default" => '#000'
            ],
            'group_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 500
            ],
            'story_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 7,
                "max" => 30,
                "default" => 14
            ],
            'story_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 400
            ],
            'mute_easy_access' => [
                "type" => 'checkbox',
                "title" => 'Sound Button Alignment',
            ],
            'primary_color' => [
                "type" => 'color',
                "title" => 'Primary',
                "default" => '#25B0CFE6'
            ],
            'secondary_color' => [
                "type" => 'color',
                "title" => 'Secondary',
                "default" => '#63A2C7C4'
            ],
            'sticky_transparency' => [
                "type" => 'slider',
                "title" => 'Background Opacity',
                "min" => 0,
                "max" => 100,
                "default" => 50
            ],
            "parent_container_padding_top" => [
                "type" => 'slider',
                "title" => 'Top Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_bottom" => [
                "type" => 'slider',
                "title" => 'Bottom Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_left" => [
                "type" => 'slider',
                "title" => 'Left Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            "parent_container_padding_right" => [
                "type" => 'slider',
                "title" => 'Right Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ]
        ],
        "open-modal2" =>  [
            'font' => [
                "type" => 'font',
                "title" => 'Font',
                "default" => 'Automatic Website Font'
            ],
            'corner_radius' => [
                "type" => 'slider',
                "title" => 'Cover Roundness',
                "min" => 0,
                "max" => 50,
                "default" => 20
            ],
            'group_name' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'hideThumbnails' => [
                "type" => 'checkbox',
                "title" => 'Hide Stories',
            ],
           'group_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 17,
                "max" => 40,
                "default" => 24
            ],
            'group_name_font_color' => [
                "type" => 'color',
                "title" => 'Font Color',
                "default" => '#000'
            ],
            'group_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 500
            ],
            'autoplay_audio' => [
                "type" => 'checkbox',
                "title" => 'Enable Unmute On Click',
            ],
            'group_title_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'thumb_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'mute_easy_access' => [
                "type" => 'checkbox',
                "title" => 'Sound Button Alignment',
            ],
            'primary_color' => [
                "type" => 'color',
                "title" => 'Primary',
                "default" => '#25B0CFE6'
            ],
            'secondary_color' => [
                "type" => 'color',
                "title" => 'Secondary',
                "default" => '#63A2C7C4'
            ],
            "parent_container_padding_top" => [
                "type" => 'slider',
                "title" => 'Top Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_bottom" => [
                "type" => 'slider',
                "title" => 'Bottom Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_left" => [
                "type" => 'slider',
                "title" => 'Left Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            "parent_container_padding_right" => [
                "type" => 'slider',
                "title" => 'Right Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ]
        ],
        "open-modal3" =>  [
            'font' => [
                "type" => 'font',
                "title" => 'Font',
                "default" => 'Automatic Website Font'
            ],
            'corner_radius' => [
                "type" => 'slider',
                "title" => 'Cover Roundness',
                "min" => 0,
                "max" => 50,
                "default" => 20
            ],
            'group_name' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'hideThumbnails' => [
                "type" => 'checkbox',
                "title" => 'Hide Stories',
            ],
            'autoplay_audio' => [
                "type" => 'checkbox',
                "title" => 'Enable Unmute On Click',
            ],
           'group_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 17,
                "max" => 40,
                "default" => 24
            ],
            'group_name_font_color' => [
                "type" => 'color',
                "title" => 'Font Color',
                "default" => '#000'
            ],
            'group_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 500
            ],
            'group_title_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'mute_easy_access' => [
                "type" => 'checkbox',
                "title" => 'Sound Button Alignment',
            ],
            'thumb_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'primary_color' => [
                "type" => 'color',
                "title" => 'Primary',
                "default" => '#25B0CFE6'
            ],
            'secondary_color' => [
                "type" => 'color',
                "title" => 'Secondary',
                "default" => '#63A2C7C4'
            ],
            'full_width' => [
                "type" => 'checkbox',
                "title" => 'Full Width',
            ],
            "parent_container_padding_top" => [
                "type" => 'slider',
                "title" => 'Top Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_bottom" => [
                "type" => 'slider',
                "title" => 'Bottom Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_left" => [
                "type" => 'slider',
                "title" => 'Left Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            "parent_container_padding_right" => [
                "type" => 'slider',
                "title" => 'Right Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ]
        ],
        "open-modal4" =>  [
            'font' => [
                "type" => 'font',
                "title" => 'Font',
                "default" => 'Automatic Website Font'
            ],
            'corner_radius' => [
                "type" => 'slider',
                "title" => 'Cover Roundness',
                "min" => 0,
                "max" => 50,
                "default" => 20
            ],
            'group_name' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'hideThumbnails' => [
                "type" => 'checkbox',
                "title" => 'Hide Stories',
            ],
           'group_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 17,
                "max" => 40,
                "default" => 24
            ],
            'group_name_font_color' => [
                "type" => 'color',
                "title" => 'Font Color',
                "default" => '#000'
            ],
            'group_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 500
            ],
            'autoplay_audio' => [
                "type" => 'checkbox',
                "title" => 'Enable Unmute On Click',
            ],
            'group_title_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'thumb_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'mute_easy_access' => [
                "type" => 'checkbox',
                "title" => 'Sound Button Alignment',
            ],
            'primary_color' => [
                "type" => 'color',
                "title" => 'Primary',
                "default" => '#25B0CFE6'
            ],
            'secondary_color' => [
                "type" => 'color',
                "title" => 'Secondary',
                "default" => '#63A2C7C4'
            ],
            "parent_container_padding_top" => [
                "type" => 'slider',
                "title" => 'Top Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_bottom" => [
                "type" => 'slider',
                "title" => 'Bottom Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_left" => [
                "type" => 'slider',
                "title" => 'Left Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            "parent_container_padding_right" => [
                "type" => 'slider',
                "title" => 'Right Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ]
        ],
        "play-in-place" =>  [
            'font' => [
                "type" => 'font',
                "title" => 'Font',
                "default" => 'Automatic Website Font'
            ],
            'group_name' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'hideThumbnails' => [
                "type" => 'checkbox',
                "title" => 'Hide Stories',
            ],
           'group_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 17,
                "max" => 40,
                "default" => 24
            ],
            'group_name_font_color' => [
                "type" => 'color',
                "title" => 'Font Color',
                "default" => '#000'
            ],
            'group_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 500
            ],
            'group_title_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'story_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 7,
                "max" => 30,
                "default" => 14
            ],
            'story_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 400
            ],
            'autoplay_audio' => [
                "type" => 'checkbox',
                "title" => 'Enable Unmute On Click',
            ],
            'corner_radius' => [
                "type" => 'slider',
                "title" => 'Cover Roundness',
                "min" => 0,
                "max" => 50,
                "default" => 20
            ],
            'storyNameDisplay' => [
                "type" => 'storyNameDisplay',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'Above Thumbnail',0=>'Inside Thumbnail',1=>'Below Thumbnail']
            ],
            'showStoryName' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'thumb_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'thumbIcon_HorizontalAlignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Horizontal Play Icon Alignment',
                "default" => 2,
                "options" => [0=>'left',2=>"center",1=>"right"]
            ],
            'hide_play_icon' => [
                "type" => 'checkbox',
                "title" => 'Hide Icon',
            ],
            'mute_easy_access' => [
                "type" => 'checkbox',
                "title" => 'Sound Button Alignment',
            ],
            'play_icon_size' => [
                "type" => 'slider',
                "title" => 'Icon Size',
                "min" => 30,
                "max" => 100,
                "default" => 40
            ],
            'thumbIcon_VerticalAlignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Vertical Play Icon Alignment',
                "default" => 2,
                "options" => [0=>'top',2=>"center",1=>"bottom"]
            ],
            'primary_color' => [
                "type" => 'color',
                "title" => 'Primary',
                "default" => '#25B0CFE6'
            ],
            'secondary_color' => [
                "type" => 'color',
                "title" => 'Secondary',
                "default" => '#63A2C7C4'
            ],
            "parent_container_padding_top" => [
                "type" => 'slider',
                "title" => 'Top Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_bottom" => [
                "type" => 'slider',
                "title" => 'Bottom Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_left" => [
                "type" => 'slider',
                "title" => 'Left Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            "parent_container_padding_right" => [
                "type" => 'slider',
                "title" => 'Right Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ]
        ],
        "play-in-place2" =>  [
            'font' => [
                "type" => 'font',
                "title" => 'Font',
                "default" => 'Automatic Website Font'
            ],
            'group_name' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'hideThumbnails' => [
                "type" => 'checkbox',
                "title" => 'Hide Stories',
            ],
           'group_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 17,
                "max" => 40,
                "default" => 24
            ],
            'group_name_font_color' => [
                "type" => 'color',
                "title" => 'Font Color',
                "default" => '#000'
            ],
            'group_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 500
            ],
            'group_title_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'story_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 7,
                "max" => 30,
                "default" => 14
            ],
            'story_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 400
            ],
            'autoplay_audio' => [
                "type" => 'checkbox',
                "title" => 'Enable Unmute On Click',
            ],
            'mute_easy_access' => [
                "type" => 'checkbox',
                "title" => 'Sound Button Alignment',
            ],
            'corner_radius' => [
                "type" => 'slider',
                "title" => 'Cover Roundness',
                "min" => 0,
                "max" => 50,
                "default" => 10
            ],
            'storyNameDisplay' => [
                "type" => 'storyNameDisplay',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'Above Thumbnail',0=>'Inside Thumbnail',1=>'Below Thumbnail']
            ],
            'showStoryName' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'thumb_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'thumbIcon_HorizontalAlignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Horizontal Play Icon Alignment',
                "default" => 2,
                "options" => [0=>'left',2=>"center",1=>"right"]
            ],
            'hide_play_icon' => [
                "type" => 'checkbox',
                "title" => 'Hide Icon',
            ],
            'play_icon_size' => [
                "type" => 'slider',
                "title" => 'Icon Size',
                "min" => 30,
                "max" => 100,
                "default" => 40
            ],
            'thumbIcon_VerticalAlignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Vertical Play Icon Alignment',
                "default" => 2,
                "options" => [0=>'top',2=>"center",1=>"bottom"]
            ],
            'primary_color' => [
                "type" => 'color',
                "title" => 'Primary',
                "default" => '#25B0CFE6'
            ],
            'secondary_color' => [
                "type" => 'color',
                "title" => 'Secondary',
                "default" => '#63A2C7C4'
            ],
            "parent_container_padding_top" => [
                "type" => 'slider',
                "title" => 'Top Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_bottom" => [
                "type" => 'slider',
                "title" => 'Bottom Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_left" => [
                "type" => 'slider',
                "title" => 'Left Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            "parent_container_padding_right" => [
                "type" => 'slider',
                "title" => 'Right Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ]
        ],
        "play-in-place3" =>  [
            'font' => [
                "type" => 'font',
                "title" => 'Font',
                "default" => 'Automatic Website Font'
            ],
            'group_name' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
           'group_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 17,
                "max" => 40,
                "default" => 24
            ],
            'group_name_font_color' => [
                "type" => 'color',
                "title" => 'Font Color',
                "default" => '#000'
            ],
            'group_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 500
            ],
            'group_title_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'story_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 7,
                "max" => 30,
                "default" => 14
            ],
            'story_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 400
            ],
            'storyNameDisplay' => [
                "type" => 'storyNameDisplay',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'Above Thumbnail',0=>'Inside Thumbnail',1=>'Below Thumbnail']
            ],
            'showStoryName' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'thumbIcon_HorizontalAlignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Horizontal Play Icon Alignment',
                "default" => 2,
                "options" => [0=>'left',2=>"center",1=>"right"]
            ],
            'hide_play_icon' => [
                "type" => 'checkbox',
                "title" => 'Hide Icon',
            ],
            'play_icon_size' => [
                "type" => 'slider',
                "title" => 'Icon Size',
                "min" => 30,
                "max" => 100,
                "default" => 30
            ],
            'thumbIcon_VerticalAlignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Vertical Play Icon Alignment',
                "default" => 2,
                "options" => [0=>'top',2=>"center",1=>"bottom"]
            ],
            'thumb_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'playStoryOnClick' => [
                "type" => 'checkbox',
                "title" => 'Play On Click',
            ],
            'useGridLayout' => [
                "type" => 'checkbox',
                "title" => 'Display As Grid',
            ],
            'arrowsOutsideContainer' => [
                "type" => 'checkbox',
                "title" => 'Arrows Outside Player Section',
            ],
            'openModalWithOverlay' => [
                "type" => 'checkbox',
                "title" => 'Open On Click',
            ],
            'hideThumbnails' => [
                "type" => 'checkbox',
                "title" => 'Hide Stories',
            ],
            'autoplay_audio' => [
                "type" => 'checkbox',
                "title" => 'Enable Unmute On Click',
            ],
            'mute_easy_access' => [
                "type" => 'checkbox',
                "title" => 'Sound Button Alignment',
            ],
            'corner_radius' => [
                "type" => 'slider',
                "title" => 'Cover Roundness',
                "min" => 0,
                "max" => 50,
                "default" => 20
            ],
            'horizontal_thumb_spacing' => [
                "type" => 'slider',
                "title" => 'Stories Horizontal Spacing',
                "min" => 0,
                "max" => 500,
                "default" => 10
            ],
            'vertical_thumb_spacing' => [
                "type" => 'slider',
                "title" => 'Stories Vertical Spacing',
                "min" => 0,
                "max" => 500,
                "default" => 10
            ],
            'border_gradient_ratio' => [
                "type" => 'slider',
                "title" => 'Border Color Ratio',
                "min" => 0,
                "max" => 100,
                "default" => 50
            ],
            'border_gradient_degree' => [
                "type" => 'slider',
                "title" => ' Border Color Mix',
                "min" => 0,
                "max" => 360,
                "default" => 0
            ],
            'border_thickness' => [
                "type" => 'slider',
                "title" => 'Border Thickness',
                "min" => 0,
                "max" => 100,
                "default" => 0
            ],
            'modal_height' => [
                "type" => 'slider',
                "title" => 'Cover Height',
                "min" => 100,
                "max" => 1000,
                "default" => 250
            ],
            'modal_width' => [
                "type" => 'slider',
                "title" => 'Cover Width',
                "min" => 100,
                "max" => 1000,
                "default" => 250
            ],
            'primary_color' => [
                "type" => 'color',
                "title" => 'Primary',
                "default" => '#25B0CFE6'
            ],
            'secondary_color' => [
                "type" => 'color',
                "title" => 'Secondary',
                "default" => '#63A2C7C4'
            ],
            'thumbnailPlaybackMode' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Autoplay',
                "default" => 0,
                "options" => [0=>'No Autoplay',1=>"Play First Only",2=>"Play in Sequence",3=>"Autoplay All"]
            ],
            "parent_container_padding_top" => [
                "type" => 'slider',
                "title" => 'Top Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_bottom" => [
                "type" => 'slider',
                "title" => 'Bottom Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_left" => [
                "type" => 'slider',
                "title" => 'Left Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            "parent_container_padding_right" => [
                "type" => 'slider',
                "title" => 'Right Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ]
        ],
        "simple" =>  [
            'font' => [
                "type" => 'font',
                "title" => 'Font',
                "default" => 'Automatic Website Font'
            ],
            'group_name' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'hideThumbnails' => [
                "type" => 'checkbox',
                "title" => 'Hide Stories',
            ],
           'group_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 17,
                "max" => 40,
                "default" => 24
            ],
            'group_name_font_color' => [
                "type" => 'color',
                "title" => 'Font Color',
                "default" => '#000'
            ],
            'group_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 500
            ],
            'story_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 7,
                "max" => 30,
                "default" => 14
            ],
            'story_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 400
            ],
            'autoplay_audio' => [
                "type" => 'checkbox',
                "title" => 'Enable Unmute On Click',
            ],
            'instagram_colors' => [
                "type" => 'checkbox',
                "title" => 'Use Instagram Colors',
            ],
            'enabledEnlargeAndSpin' => [
                "type" => 'checkbox',
                "title" => 'Hover Effect',
            ],
            'thumbnailPlaybackMode' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Autoplay',
                "default" => 0,
                "options" => [0=>'No Autoplay',1=>"Play First Only",2=>"Play in Sequence",3=>"Autoplay All"]
            ],
            'mute_easy_access' => [
                "type" => 'checkbox',
                "title" => 'Sound Button Alignment',
            ],
            'storyNameDisplay' => [
                "type" => 'storyNameDisplay',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [0=>'Below Thumbnail',2=>'Inside Thumbnail',1=>'Above Thumbnail',3=>'Right Side',4=>'Left Side']
            ],
            'showStoryName' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'thumb_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'thumb_size' => [
                "type" => 'slider',
                "title" => 'Cover Size',
                "min" => 50,
                "max" => 500,
                "default" => 80
            ],
            'thumbIcon_HorizontalAlignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Horizontal Play Icon Alignment',
                "default" => 2,
                "options" => [0=>'left',2=>"center",1=>"right"]
            ],
            'hide_play_icon' => [
                "type" => 'checkbox',
                "title" => 'Hide Icon',
            ],
            'play_icon_size' => [
                "type" => 'slider',
                "title" => 'Icon Size',
                "min" => 30,
                "max" => 70,
                "default" => 30
            ],
            'thumbIcon_VerticalAlignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Vertical Play Icon Alignment',
                "default" => 2,
                "options" => [0=>'top',2=>"center",1=>"bottom"]
            ],
            'horizontal_thumb_spacing' => [
                "type" => 'slider',
                "title" => 'Stories Horizontal Spacing',
                "min" => -400,
                "max" => 500,
                "default" => 10
            ],
            'primary_color' => [
                "type" => 'color',
                "title" => 'Primary',
                "default" => '#25B0CFE6'
            ],
            'secondary_color' => [
                "type" => 'color',
                "title" => 'Secondary',
                "default" => '#63A2C7C4'
            ],
            "parent_container_padding_top" => [
                "type" => 'slider',
                "title" => 'Top Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_bottom" => [
                "type" => 'slider',
                "title" => 'Bottom Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_left" => [
                "type" => 'slider',
                "title" => 'Left Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            "parent_container_padding_right" => [
                "type" => 'slider',
                "title" => 'Right Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ]
        ],
        "scroll-grid" =>  [
            'font' => [
                "type" => 'font',
                "title" => 'Font',
                "default" => 'Automatic Website Font'
            ],
            'group_name' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'hideThumbnails' => [
                "type" => 'checkbox',
                "title" => 'Hide Stories',
            ],
            'autoplay_audio' => [
                "type" => 'checkbox',
                "title" => 'Enable Unmute On Click',
            ],
            'mute_easy_access' => [
                "type" => 'checkbox',
                "title" => 'Sound Button Alignment',
            ],
           'group_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 17,
                "max" => 40,
                "default" => 24
            ],
            'group_name_font_color' => [
                "type" => 'color',
                "title" => 'Font Color',
                "default" => '#000'
            ],
            'group_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 500
            ],
            'group_title_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'story_name_font_size' => [
                "type" => 'slider',
                "title" => 'Font Size',
                "min" => 7,
                "max" => 30,
                "default" => 10
            ],
            'story_name_font_thickness' => [
                "type" => 'slider',
                "title" => 'Font Thickness',
                "min" => 100,
                "max" => 900,
                "step" => 100,
                "default" => 400
            ],
            'corner_radius' => [
                "type" => 'slider',
                "title" => 'Cover Roundness',
                "min" => 0,
                "max" => 50,
                "default" => 20
            ],
            'storyNameDisplay' => [
                "type" => 'storyNameDisplay',
                "title" => 'Alignment',
                "default" => 0,
                "options" => [2=>'Above Thumbnail',0=>'Inside Thumbnail',1=>'Below Thumbnail']
            ],
            'showStoryName' => [
                "type" => 'checkbox',
                "title" => 'Show Title',
            ],
            'thumb_alignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Cover Alignment',
                "default" => 0,
                "options" => [2=>'left',0=>'center',1=>'right']
            ],
            'thumbIcon_HorizontalAlignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Horizontal Play Icon Alignment',
                "default" => 2,
                "options" => [0=>'left',2=>"center",1=>"right"]
            ],
            'hide_play_icon' => [
                "type" => 'checkbox',
                "title" => 'Hide Icon',
            ],
            'play_icon_size' => [
                "type" => 'slider',
                "title" => 'Icon Size',
                "min" => 30,
                "max" => 70,
                "default" => 30
            ],
            'thumbIcon_VerticalAlignment' => [
                "type" => 'dropdown_options_list',
                "title" => 'Vertical Play Icon Alignment',
                "default" => 2,
                "options" => [0=>'top',2=>"center",1=>"bottom"]
            ],
            'primary_color' => [
                "type" => 'color',
                "title" => 'Primary',
                "default" => '#25B0CFE6'
            ],
            'secondary_color' => [
                "type" => 'color',
                "title" => 'Secondary',
                "default" => '#63A2C7C4'
            ],
            "parent_container_padding_top" => [
                "type" => 'slider',
                "title" => 'Top Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_bottom" => [
                "type" => 'slider',
                "title" => 'Bottom Padding',
                "min" => 0,
                "max" => 100,
                "default" => 10
            ],
            "parent_container_padding_left" => [
                "type" => 'slider',
                "title" => 'Left Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ],
            "parent_container_padding_right" => [
                "type" => 'slider',
                "title" => 'Right Padding',
                "min" => 0,
                "max" => 100,
                "default" => 1
            ]
        ],
    ]
];
