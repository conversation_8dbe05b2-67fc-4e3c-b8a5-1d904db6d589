<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Group;

use App\Models\User;
use Illuminate\Http\Request;

class ConsumptionController extends Controller
{
    public function __invoke(Request $request, Group $group)
    {
        $user = User::where('id', $group->user_id)->firstOrFail();
        return response(['reached_the_limit' => $user->reached_limit()]);

    }
}
