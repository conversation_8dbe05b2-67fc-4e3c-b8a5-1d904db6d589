import { html } from "lit";
import {state} from 'lit/decorators.js';
import { BaseSmartStory } from "../../base-smart-story";

class SmartStoryDynamic extends BaseSmartStory {
    static styles = [BaseSmartStory.getStyles()]
    @state() skew = true
    debounce(func:any, delay = 0) {
        let timeoutId:number
        return () => {
          clearTimeout(timeoutId)
          timeoutId = setTimeout(() => {
            func.apply(this, arguments)
          }, delay)
        }
    }

    render() {

        const horizontalAlignment = this.settings?.thumbIcon_HorizontalAlignment || "2";
        const verticalAlignment = this.settings?.thumbIcon_VerticalAlignment || "2";

        const alignmentClasses = `
        ${horizontalAlignment === "0" ? "left-2" : horizontalAlignment === "1" ? "right-2":"left-1/2 transform -translate-x-1/2"}
        ${verticalAlignment === "0" ? "top-2" : verticalAlignment === "1" ? "bottom-2":"top-1/2 transform -translate-y-1/2"}
        `;

        const hidePlayIcon = this.settings?.hide_play_icon ?? false;
        const playIconSize = this.settings?.play_icon_size || 40;

        // Determine if we need side positioning for layout
        const isLeftSide = (this.settings?.showStoryName ?? true) && this.settings?.storyNameDisplay == "4";
        const isRightSide = (this.settings?.showStoryName ?? true) && this.settings?.storyNameDisplay == "3";
        const needsSideLayout = isLeftSide || isRightSide;

        return html`
            <div class="flex ${needsSideLayout ? 'items-center' : 'flex-col'} h-full ${needsSideLayout ? '' : 'justify-between'} ${needsSideLayout ? '' : 'w-32'} mx-2">
                ${isLeftSide ? html`
                    <!-- story name left side of thumbnail -->
                    <div class="flex items-center mr-2" style="max-width: 80px;">
                        <div class="text-center transform duration-300 text-black px-1"
                            style="font-weight: ${this.settings?.story_name_font_thickness || 400};font-size:${this.settings.story_name_font_size || 14 }px;font-family:${this.settings?.font}; display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:3;overflow: hidden;line-height:1.2; word-break: break-word;"
                        >
                            ${this.story.name}
                        </div>
                    </div>
                ` : null}

                <div class="${needsSideLayout ? '' : 'flex flex-col h-full justify-between w-32'}">
                    ${(this.settings?.showStoryName ?? true) && this.settings?.storyNameDisplay == "2" ? html`
                        <div style="font-weight: ${this.settings?.story_name_font_thickness || 400};font-size:${this.settings.story_name_font_size || 14 }px;font-family:${this.settings?.font}; display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:3;overflow: hidden;line-height:1.2;"
                             class="w-full mb-2 px-1 text-black text-center transform duration-300 group-hover:opacity-0">
                            ${this.story.name}
                        </div>
                    `: null}
                <button aria-label="Open Story"  @mouseenter="${()=>this.skew = !this.skew}" @mouseleave="${this.debounce(()=>{this.skew = this.settings?.skew_sync?this.skew:!this.skew},50)}" class="w-28 h-32 relative block group mx-auto">
                    <div class="${this.settings?.fill ? '' : 'border'} w-full h-full duration-300 transform"
                        style="
                            --tw-skew-y: -${this.skew ? 14 : 0}deg;
                            transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
                            --tw-skew-x: ${this.skew ? 14 : 0}deg;
                            ${this.settings?.fill ?
                                `background-color: rgba(${parseInt(this.settings?.secondary_color.slice(1, 3), 16)},
                                                        ${parseInt(this.settings?.secondary_color.slice(3, 5), 16)},
                                                        ${parseInt(this.settings?.secondary_color.slice(5, 7), 16)},
                                                        ${this.settings?.sticky_transparency / 100});`
                                :
                                `border-color: rgba(${parseInt(this.settings?.secondary_color.slice(1, 3), 16)},
                                                    ${parseInt(this.settings?.secondary_color.slice(3, 5), 16)},
                                                    ${parseInt(this.settings?.secondary_color.slice(5, 7), 16)},
                                                    ${this.settings?.sticky_transparency / 100});`
                            }
                            border-radius: ${this.settings?.corner_radius}px;
                        "
                    ></div>
                    <div class="w-full h-full absolute bottom-0">
                        <img class="w-full h-full object-cover" loading="lazy"
                            src="${this.story.thumbnail_resized ? this.story.thumbnail_resized : this.story.thumbnail_url ? this.story.thumbnail_url : `${this.appUrl}/images/placeholder/thumbnail.png`}"
                            alt=""
                            style="border-radius: ${this.settings?.corner_radius}px"
                        />
                        <div class="flex items-center justify-center w-full h-full absolute bottom-0" style="border-radius: ${this.settings?.corner_radius}px" >
                            <div>
                                ${(this.settings?.showStoryName ?? true) && this.settings?.storyNameDisplay == "0" ? html`
                                    <div style="font-weight: ${this.settings?.story_name_font_thickness || 400};font-size:${this.settings.story_name_font_size || 14 }px;font-family:${this.settings?.font}; display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:2;overflow: hidden;line-height:1.1;" class="absolute w-full left-0 top-0 mt-1.5 mx-2 text-white text-center transform duration-300 group-hover:opacity-0">
                                        ${this.story.name}
                                    </div>
                                ` : null}
                                ${!hidePlayIcon ? html`
                                    <div style="width:${playIconSize}px;height:${playIconSize}px;" class="absolute ${alignmentClasses} transform duration-300 group-hover:scale-125">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="w-full h-full text-white opacity-70" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6">
                                            <path fill-rule="evenodd" d="M4.5 5.653c0-1.426 1.529-2.33 2.779-1.643l11.54 6.348c1.295.712 1.295 2.573 0 3.285L7.28 19.991c-1.25.687-2.779-.217-2.779-1.643V5.653z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                ` : null}
                            </div>
                        </div>
                    </div>
                </button>
                    ${(this.settings?.showStoryName ?? true) && this.settings?.storyNameDisplay == "1" ? html`
                        <div style="font-weight: ${this.settings?.story_name_font_thickness || 400};font-size:${this.settings.story_name_font_size || 14 }px;font-family:${this.settings?.font}; display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:3;overflow: hidden;line-height:1.1;"
                        class="w-full mt-2 px-1 text-black text-center transform duration-300 group-hover:opacity-0">
                            ${this.story.name}
                        </div>
                    ` : null}
                </div>

                ${isRightSide ? html`
                    <!-- story name right side of thumbnail -->
                    <div class="flex items-center ml-2" style="max-width: 80px;">
                        <div class="text-center transform duration-300 text-black px-1"
                            style="font-weight: ${this.settings?.story_name_font_thickness || 400};font-size:${this.settings.story_name_font_size || 14 }px;font-family:${this.settings?.font}; display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:3;overflow: hidden;line-height:1.2; word-break: break-word;"
                        >
                            ${this.story.name}
                        </div>
                    </div>
                ` : null}
            </div>
        `
    }
}
if (!customElements.get('smart-story-dynamic')) customElements.define('smart-story-dynamic', SmartStoryDynamic);