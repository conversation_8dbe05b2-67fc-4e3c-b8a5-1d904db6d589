<?php

namespace App\Http\Controllers\Api\Groups;

use App\Http\Controllers\Controller;
use App\Models\Group;
use App\Models\Story;
use Illuminate\Http\Request;

class GroupViewsController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function __invoke(Request $request, Group $group, Story $story)
    {

        if ($group->preview_secret === request('preview_secret')) {
            return;
        }

        views($story)->record();
    }
}
