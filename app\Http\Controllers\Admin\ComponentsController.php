<?php

namespace App\Http\Controllers\Admin;

use App\Models\Component;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Spatie\QueryBuilder\QueryBuilder;
use Illuminate\Auth\Events\Registered;


class ComponentsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $components = QueryBuilder::for(Component::class)
        ->allowedFilters(['id', 'title', 'tag'])
        ->orderBy('created_at', 'DESC')
        ->with(['storyMediaPlayerCustomElements', 'shopifyGroupCustomElements'])
        ->paginate(request('per_page', 10))
        ->appends(request()->query());

    $components->each(function ($component) {
        $component->total_usage_count = $component->getTotalUsageCount();
    });

        return response(['success' => true, 'data' => $components]);
    }

    public function show(Component $component)
    {
        return response(['success' => true, 'data' => $component]);
    }

    public function store(){
        $data=\request()->validate([
            'title'=>['required'],
            'description'=>[],
            'schema'=>['required'],
            'image'=>['required'],
            'components_category_id'=>['required'],
            'script_link'=>['required'],
            'tag'=>['required'],
            'is_active' => ['boolean', 'required'],
        ]);
        
        $component = new Component();
        $component->schema = json_decode($data['schema'],true);
        $component->title = $data['title'];
        $component->description = $data['description'];
        $component->image = $data['image'];
        $component->components_category_id = $data['components_category_id'];
        $component->script_link = $data['script_link'];
        $component->tag = $data['tag'];
        $component->is_active = $data['is_active'];
        $component->save();
        
        return response(['success' => true, 'data' => $component]);
    }


    
    public function update(Component $component){
        $data=\request()->validate([
            'title'=>['required'],
            'description'=>[],
            'schema'=>['required'],
            'image'=>['required'],
            'components_category_id'=>['required'],
            'script_link'=>['required'],
            'tag'=>['required'],
            'is_active' => ['boolean'],
        ]);
        
        $component->schema = json_decode($data['schema'],true);
        $component->title = $data['title'];
        $component->description = $data['description'];
        $component->image = $data['image'];
        $component->components_category_id = $data['components_category_id'];
        $component->script_link = $data['script_link'];
        $component->tag = $data['tag'];
        $component->is_active = $data['is_active'] ?? 1;
        $component->save();
        return response(['success' => true, 'data' => $component]);
    }
}
