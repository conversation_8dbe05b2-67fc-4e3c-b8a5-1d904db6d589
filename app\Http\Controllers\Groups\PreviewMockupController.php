<?php

namespace App\Http\Controllers\Groups;

use Throwable;
use App\Models\Group;
use App\Models\Story;
use App\Models\StoryMedia;
use Illuminate\Http\Request;
use App\Http\Traits\ShopifyTrait;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Models\ShopifyGroupCustomElement;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Storage;

class PreviewMockupController extends Controller
{
    use ShopifyTrait;
    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public $upperImage_url = "";
    public $lowerImage_url = "";    
    public $upperImageMobile_url = "";
    public $lowerImageMobile_url = "";
    public $bgColor = "#F9FAFB";
    public $is_authorized = false;
    public function __invoke(Request $request, Group $group) {
        $this->is_authorized = false;

        // Check if a user is authenticated and if group is not null
        if (Auth::check() && $group) {
            $this->is_authorized = $group->user_id == Auth::user()->id;
        }
        $templates = \App\Models\GroupTemplate::orderBy('order')->get();
        
        if(request('template'))
            $group->template = request('template');

        // if($group->channel == 'shopify' && $group->type == 'autogenerated'){
        //     $main_shop = auth()->user()->shopify_domain;
        //     $shop = str_replace(".myshopify.com", "",$main_shop);
        //     $token = auth()->user()->shopify_token;
        //     $products = $this->getProducts($shop, $token);
        //     if($group->content){
        //         $random_product = isset($products) ? $products[rand(0,count($products)-1)] : NULL;
        //         $extra_details = [
        //             'product' => $random_product['id'] ?? NULL,
        //             'collection' => $group->collection_handle,
        //         ];
        //         try{
        //             $products_stories = $this->getProductsFiltered($main_shop,$group->content,$extra_details);
        //             if($products_stories == null) $products_stories = $products;
        //         }catch(Throwable $e) {
        //             $products_stories = $products;
        //         }
        //     }else{
        //         $products_stories = $products;
        //     }

        //     $manual_stories =[];
        //     $counter = 0;
        //     $custom_elements = ShopifyGroupCustomElement::where('group_id',$group->id)->get();
        //     foreach($products_stories as $k=>$product){
        //         if($counter<10){
        //             $counter++;
        //             $manual_stories[$k] = new Story();
        //             $manual_stories[$k]->id = $product['id'];
        //             $manual_stories[$k]->group_id = $group->id;
        //             $manual_stories[$k]->name = $product['title'];
        //             $manual_stories[$k]->tags = [];
        //             $manual_stories[$k]->thumbnail = null;
        //             if(isset($product['images'][0]))
        //                 $manual_stories[$k]->thumbnail_resized = $product['images'][0]['src'] ?? 'https:'.$product['images'][0];
        //             else
        //                 $manual_stories[$k]->thumbnail_resized = asset('images/placeholder/thumbnail.png');
        //             $manual_stories[$k]->note = null;
        //             $manual_stories[$k]->order_column = 0;
        //             $manual_stories[$k]->media[0] = new StoryMedia();
        //             $manual_stories[$k]->media[0]->id = $product['id'];
        //             $manual_stories[$k]->media[0]->player_custom_elements = (object) $custom_elements;
        //             $manual_stories[$k]->media[0]->type = 'image';
        //             $manual_stories[$k]->media[0]->title_enabled = false;
        //             $manual_stories[$k]->media[0]->mute_original_video = false;
        //             $manual_stories[$k]->media[0]->animate_title = false;
        //             $manual_stories[$k]->media[0]->style = $group->customizations['style'] ?? '';
        //             $manual_stories[$k]->media[0]->animation = $group->customizations['animation'] ?? '';
        //             $manual_stories[$k]->media[0]->animationFrom = $group->customizations['animationFrom'] ?? null;
        //             $manual_stories[$k]->media[0]->animationTo = $group->customizations['animationTo'] ?? null;
        //             $manual_stories[$k]->media[0]->animationEasing = $group->customizations['animationEasing'] ?? null;
        //             $manual_stories[$k]->media[0]->animationEasingType = $group->customizations['animationEasingType'] ?? null;
        //             $manual_stories[$k]->media[0]->background_color = $group->background_color;
        //             $manual_stories[$k]->media[0]->actions = [];
        //             if(isset($product['images'][0])){
        //                 $manual_stories[$k]->media[0]->image = $product['images'][0]['src'] ?? 'https:'.$product['images'][0];
        //                 $manual_stories[$k]->media[0]->image_url = $product['images'][0]['src'] ?? 'https:'.$product['images'][0];
        //             }else{
        //                 $manual_stories[$k]->media[0]->image = asset('images/placeholder/thumbnail.png');
        //                 $manual_stories[$k]->media[0]->image_url = asset('images/placeholder/thumbnail.png');
        //             }

        //         }

        //     }
        //     $group->setRelation('stories', $manual_stories);
        // }
        $this->handleContentDownload($group);
        
        return view( 'groups.previewMockup',  compact('group', 'templates'))->with([
                'is_authorized'=>$this->is_authorized,
                'lowerImage_url' => $this->lowerImage_url, 
                'upperImage_url' => $this->upperImage_url, 
                'lowerImageMobile_url' => $this->lowerImageMobile_url, 
                'upperImageMobile_url' => $this->upperImageMobile_url, 
                'bgColor' => $this->bgColor
            ]);
    }

    public function handleContentDownload(Group $group) {
        //initialize variables
        $bgColor = null;
        $s3UrlForUpperImage = null;
        $s3UrlForLowerImage = null;
        $s3UrlForUpperImageMobile = null;
        $s3UrlForLowerImageMobile = null;

        // Generate a unique name based on the group secret for the image
        $uniqueNameForUpperImage = hash('sha256', $group->secret) . '_upperImage';
        $upperImage_path = 'previewMockup/images/' . $uniqueNameForUpperImage . '.webp';
        if (Storage::disk('s3')->exists($upperImage_path)) {
            $s3UrlForUpperImage = Storage::disk('s3')->url($upperImage_path); // Get the direct link to the uploaded image on S3
        }        
        
        $uniqueNameForLowerImage = hash('sha256', $group->secret) . '_lowerImage';
        $lowerImage_path = 'previewMockup/images/' . $uniqueNameForLowerImage . '.webp';
        if (Storage::disk('s3')->exists($lowerImage_path)) {
            $s3UrlForLowerImage = Storage::disk('s3')->url($lowerImage_path);
        }

        $uniqueNameForUpperImageMobile = hash('sha256', $group->secret) . '_upperImageMobile';
        $upperImageMobile_path = 'previewMockup/images/' . $uniqueNameForUpperImageMobile . '.webp';
        if (Storage::disk('s3')->exists($upperImageMobile_path)) {
            $s3UrlForUpperImageMobile = Storage::disk('s3')->url($upperImageMobile_path);
        }
        
        $uniqueNameForLowerImageMobile = hash('sha256', $group->secret) . '_lowerImageMobile';
        $lowerImageMobile_path = 'previewMockup/images/' . $uniqueNameForLowerImageMobile . '.webp';
        if (Storage::disk('s3')->exists($lowerImageMobile_path)) {
            $s3UrlForLowerImageMobile = Storage::disk('s3')->url($lowerImageMobile_path);
        }
                        
        $uniqueNameForBgColor = hash('sha256', $group->secret) . '_bgcolor.txt';
        $bgColor_path = 'previewMockup/bgcolors/' . $uniqueNameForBgColor;
        if (Storage::disk('s3')->exists($bgColor_path)) {
            $bgColor = Storage::disk('s3')->get($bgColor_path);
        }        
    
        $this->bgColor = $bgColor?$bgColor:$this->bgColor;
        $this->lowerImage_url = $s3UrlForLowerImage?$s3UrlForLowerImage:$this->lowerImage_url;
        $this->upperImage_url = $s3UrlForUpperImage?$s3UrlForUpperImage:$this->upperImage_url;
        $this->lowerImageMobile_url = $s3UrlForLowerImageMobile?$s3UrlForLowerImageMobile:$this->lowerImageMobile_url;
        $this->upperImageMobile_url = $s3UrlForUpperImageMobile?$s3UrlForUpperImageMobile:$this->upperImageMobile_url;

        return response()->json([
            'lowerImage_path' => $this->lowerImage_url,
            'upperImage_path' => $this->upperImage_url,
            'lowerImageMobile_path' => $this->lowerImageMobile_url,
            'upperImageMobile_path' => $this->upperImageMobile_url,
            'background_color' => $this->bgColor
        ]);
    }
public function handleContentUpload(Request $request, Group $group) {
    // Handle the image
    $uploadedUpperImage = $request->file('upperImage');
    
    // Process the image
    $upperImage = Image::make($uploadedUpperImage->path());
    
    // Generate a unique name based on the group secret for the image
    $uniqueNameForUpperImage = hash('sha256', $group->secret) . '_upperImage';
    $upperImage_path = 'previewMockup/images/' . $uniqueNameForUpperImage . '.webp';
    
    // Stream write the image to S3
    Storage::disk('s3')->put(
        $upperImage_path, 
        $upperImage->stream('webp', 80)
    );
    
    // Get the direct link to the uploaded image on S3
    $s3UrlForUpperImage = Storage::disk('s3')->url($upperImage_path);

    
    // Handle the image
    $uploadedLowerImage = $request->file('lowerImage');
    $lowerImage = Image::make($uploadedLowerImage->path());
    
    $uniqueNameForLowerImage = hash('sha256', $group->secret) . '_lowerImage';
    $lowerImage_path = 'previewMockup/images/' . $uniqueNameForLowerImage . '.webp';
    
    Storage::disk('s3')->put(
        $lowerImage_path, 
        $lowerImage->stream('webp', 80)
    );
    
    // Get the direct link to the uploaded image on S3
    $s3UrlForLowerImage = Storage::disk('s3')->url($lowerImage_path);
    
    // Handle the image
    $uploadedUpperImageMobile = $request->file('upperImageMobile');

    // Process the image
    $upperImageMobile = Image::make($uploadedUpperImageMobile->path());
    
    // Generate a unique name based on the group secret for the image
    $uniqueNameForUpperImageMobile = hash('sha256', $group->secret) . '_upperImageMobile';
    $upperImageMobile_path = 'previewMockup/images/' . $uniqueNameForUpperImageMobile . '.webp';
    
    // Stream write the image to S3
    Storage::disk('s3')->put(
        $upperImageMobile_path, 
        $upperImageMobile->stream('webp', 80)
    );
    
    // Get the direct link to the uploaded image on S3
    $s3UrlForUpperImageMobile = Storage::disk('s3')->url($upperImageMobile_path);

    
    // Handle the image
    $uploadedLowerImageMobile = $request->file('lowerImageMobile');
    $lowerImageMobile = Image::make($uploadedLowerImageMobile->path());
    
    $uniqueNameForLowerImageMobile = hash('sha256', $group->secret) . '_lowerImageMobile';
    $lowerImageMobile_path = 'previewMockup/images/' . $uniqueNameForLowerImageMobile . '.webp';
    
    Storage::disk('s3')->put(
        $lowerImageMobile_path, 
        $lowerImageMobile->stream('webp', 80)
    );
    
    // Get the direct link to the uploaded image on S3
    $s3UrlForLowerImageMobile = Storage::disk('s3')->url($lowerImageMobile_path);
        
    // Handle the background color
    $bgColor = $request->input('bgColorPicker');
    
    // Generate a unique name based on the group secret for the background color
    $uniqueNameForBgColor = hash('sha256', $group->secret) . '_bgcolor.txt';
    $bgColor_path = 'previewMockup/bgcolors/' . $uniqueNameForBgColor;
    
    // Write the background color to S3
    Storage::disk('s3')->put(
        $bgColor_path, 
        $bgColor
    );
    $this->bgColor = $bgColor;
    $this->upperImage_url = $s3UrlForUpperImage;
    $this->lowerImage_url = $s3UrlForLowerImage;
    $this->upperImageMobile_url = $s3UrlForUpperImageMobile;
    $this->lowerImageMobile_url = $s3UrlForLowerImageMobile;
    return response()->json([
        'upperImage_path' => $s3UrlForUpperImage,
        'lowerImage_path' => $s3UrlForLowerImage,
        'upperImageMobile_path' => $s3UrlForUpperImageMobile,
        'lowerImageMobile_path' => $s3UrlForLowerImageMobile,
        'background_color' => $bgColor // This returns the actual color code
    ]);
}
}