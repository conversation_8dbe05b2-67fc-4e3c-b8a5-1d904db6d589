<?php

namespace App\Http\Controllers\Admin\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Rules\CorrectUserCredentials;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class AuthController extends Controller
{
    public function login(Request $request)
    {
        if (Auth::attempt(['email' => $request->email, 'password' => $request->password])) {
            $authUser = Auth::user();
            if ($authUser->admin==1) {
                $authUser->ip_address = getIp();
                $authUser->save();

                return [
                    'user' => $authUser,
                    'token' => $authUser->createToken($request->ip())->accessToken
                ];
            }
            return response(['success' => false, 'message' => 'unauthorized'], 401);
        }
        return response(['success' => false, 'message' => 'unauthorized'], 401);
    }

    public function signup(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'required|email',
            'password' => 'required',
            'confirm_password' => 'required|same:password',
        ]);

        if ($validator->fails()) {
            return response(['success' => false, 'message' => 'validation error']);
        }

        $input = $request->all();
        $input['password'] = bcrypt($input['password']);
        $input['admin'] = 1;
        $user = User::create($input);

        $user->createToken($request->ip())->accessToken;

        return response(['success' => true, 'message' => 'user created']);
    }

    public function logout(Request $request)
    {
        auth()->guard('web')->logout();

        return response()->json([
            'message' => 'User Logout',
        ]);
    }

    public function index()
    {
        $user = Auth::user();

        return response(['name' => $user->name, 'email' => $user->email]);
    }

    public function update()
    {
        $user = Auth::user();
        $data = request()->validate([
            'password' => ['same:confirm-password'],
            'name' => [],
        ]);

        if (request()->has('password'))
            $data['password'] = bcrypt($data['password']);

        $user->update($data);

        return response(['success' => true, 'data' => 'admin updated']);
    }
}
