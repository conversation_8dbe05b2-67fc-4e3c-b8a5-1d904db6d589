<?php

namespace App\Http\Controllers\Billing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class BillingController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function __invoke(Request $request)
    {
        /**  @var \App\Models\User */
        $user = auth()->user();
        
        $monthlyStandard = config('paddle.plans.monthly.standard');
        $monthlyPremium =  config('paddle.plans.monthly.premium');
        $subscribed = $user->subscribed();

        if ($subscribed) {
            $paylinkStandard = url("/billing/{$monthlyStandard}/swap");
            $paylinkPremium  = url("/billing/{$monthlyPremium}/swap");

        } else {
            $paylinkStandard = $user->newSubscription('default', $monthlyStandard)
                ->returnTo(route('dashboard'))
                ->create();

            $paylinkPremium = $user->newSubscription('default', $monthlyPremium)
                ->returnTo(route('dashboard'))
                ->create();
        }

        return view('billing.index', compact('subscribed', 'paylinkStandard', 'paylinkPremium', 'monthlyStandard', 'monthlyPremium'));
    }

    public function free_subscription(Request $request)
    {
        auth()->user()->free_subscription = 1;
        auth()->user()->save();
        return redirect()->route('dashboard');
    }

    public function cancel_free_subscription(Request $request)
    {
        auth()->user()->free_subscription = 0;
        auth()->user()->save();
        return redirect()->route('profile.billing');
    }
    
}
