<?php

namespace App\Http\Controllers\Admin;

use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Subscription;
use Spatie\QueryBuilder\QueryBuilder;
use Illuminate\Auth\Events\Registered;


class UsersPlanController extends Controller
{
    
    public function update(){
        $data=\request()->validate([
            'user_id'=>['required'],
            'subscription_id' => ['required','exists:subscriptions,id'],
            'plan'=>['required','in:Premium Plan,Standard Plan,Free Plan, Enterprise Plan'],
        ]);

        // $user = User::find($data['user_id']);
        $subscription = Subscription::where(['id'=>$data['subscription_id'],'billable_id'=>$data['user_id']])->first();
        if($subscription){
            $subscription->paddle_plan = $data['plan'];
            $subscription->save();
            return response(['success' => true,'message'=>'Plan Updated']);
        }else{
            return response(['success' => false],404);
        }
    }

}
