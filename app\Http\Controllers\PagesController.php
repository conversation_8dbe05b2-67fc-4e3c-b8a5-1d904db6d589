<?php

namespace App\Http\Controllers;

use App\Models\Page;
use Illuminate\Http\Request;

class PagesController extends Controller
{
    public function show(Page $page)
    {
        if (!$page->active) {
            abort(404);
        }

        return view('page', compact('page'));
    }

    public function policy()
    {
        return view('policy');
    }
    public function terms()
    {
        return view('terms');
    }
}
