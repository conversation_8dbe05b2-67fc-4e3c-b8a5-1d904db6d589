<?php

namespace App\Http\Controllers\Billing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use GuzzleHttp\Psr7\Request as GuzzleRequest;
use GuzzleHttp\Client;
use App\Models\Subscription;

class CancelController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function __invoke(Request $request)
    {
        /**  @var \App\Models\User */
        $user = auth()->user();
        $client = new Client();
        if($request->input('shopify')==1){
            $headers = [
                'X-Shopify-Access-Token' => $user->shopify_token,
                'Content-Type' => 'application/json',
                'Cookie' => 'request_method=POST'
            ];
            $body = '{"query":"{currentAppInstallation {activeSubscriptions {id,status,name}}}","variables":{}}';
            $request = new GuzzleRequest('POST', 'https://'.$user->shopify_domain.'/admin/api/'.config('shopify.api_version').'/graphql.json', $headers, $body);
            try{
                $res = $client->sendAsync($request)->wait();
                $data = json_decode($res->getBody());
                if(!empty($data->data->currentAppInstallation->activeSubscriptions) && $data->data->currentAppInstallation->activeSubscriptions[0]->status == 'ACTIVE'){       
                    $subscription_id = $data->data->currentAppInstallation->activeSubscriptions[0]->id;
                    // $cancel_request = 'mutation { appSubscriptionCancel( id: "'.$subscription_id.'") { appSubscription { id status} }';
                    $headers = [
                        'X-Shopify-Access-Token' => $user->shopify_token,
                        'Content-Type' => 'application/graphql',
                    ]; 
                    $body = 'mutation { appSubscriptionCancel( id: "'.$subscription_id.'"  ) { userErrors { field message } appSubscription { id status} } }';
                    $client = new Client();                  
                    $cancel_request = new GuzzleRequest('POST', 'https://'.$user->shopify_domain.'/admin/api/'.config('shopify.api_version').'/graphql.json', $headers, $body);
                    $cancel_subscription = $client->sendAsync($cancel_request)->wait();
                    $cancel = json_decode($cancel_subscription->getBody());
                    $subscriptions = Subscription::where('paddle_id','=',$subscription_id);
                    if($subscriptions->count() > 0){
                        $subscriptions->update(['paddle_status' => 'canceled']);             
                    } 
                                
                }
            }
            catch (\GuzzleHttp\Exception\ClientException $e) {
     
            }  
        }else{

            // $user->subscription()->cancelNow();
            

        }
        
        

        return redirect('/profile/billing');
    }
}
