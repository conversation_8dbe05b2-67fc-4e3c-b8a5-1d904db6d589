<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\QueryBuilder;

class ActiveSubscriptionController extends Controller
{
    public function index()
    {

        $users = QueryBuilder::for(User::class)
            ->where('admin', 0)
            ->whereHas('subscriptions', function ($query) {
                $query->where('paddle_plan', '!=', 'Free Plan')
                    ->where('ends_at', '>=', Carbon::today())
                    ->where('paddle_status', '=', 'active');
            })
            ->allowedFilters(['id', 'name', 'email'])
            ->distinct('users.id')
            ->orderBy('created_at', 'DESC')
            ->paginate(request('per_page', 10))
            ->appends(request()->query());

        return response(['success' => true, 'data' => $users]);

    }
}
