<?php

namespace App\Http\Controllers\Admin;

use App\Models\GroupTemplate;
use App\Models\Group;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Spatie\QueryBuilder\QueryBuilder;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\DB;


class GroupTemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $templates = QueryBuilder::for(GroupTemplate::class)
        ->allowedFilters(['id', 'title', 'slug'])
        ->orderBy('order', 'ASC')
        ->withCount('groups') // This will add a 'groups_count' attribute to each GroupTemplate
        ->addSelect([
            'impressions' => Group::selectRaw('SUM(stats_bars.impressions)')
                ->join('stats_bars', 'stats_bars.user_id', '=', 'groups.user_id')
                ->whereColumn('groups.template', 'groups_templates.slug')
                ->groupBy('groups.template'),
            'views' => Group::selectRaw('SUM(stats_bars.total_views)')
                ->join('stats_bars', 'stats_bars.user_id', '=', 'groups.user_id')
                ->whereColumn('groups.template', 'groups_templates.slug')
                ->groupBy('groups.template'),
            'conversion_rate_total' => Group::selectRaw('SUM(stats_bars.conversion_rate_total)')
                ->join('stats_bars', 'stats_bars.user_id', '=', 'groups.user_id')
                ->whereColumn('groups.template', 'groups_templates.slug')
                ->groupBy('groups.template')
        ])
        ->paginate(request('per_page', 10))
        ->appends(request()->query());

     return response(['success' => true, 'data' => $templates]);
    }

    public function show(GroupTemplate $group_template)
    {
        return response(['success' => true, 'data' => $group_template]);
    }

    public function store(){
        $data=\request()->validate([
            'slug'=>['required'],
            'title'=>['required'],
            'text'=>['required'],
            'image'=>['required'],
            'groups_templates_category_id'=>['required','exists:templates_categories,id'],
            'order'=>['required'],

        ]);

        $template = new GroupTemplate();
        $template->slug = $data['slug'];
        $template->title = $data['title'];
        $template->text = $data['text'];
        $template->image = $data['image'];
        $template->groups_templates_category_id = $data['groups_templates_category_id'];
        $template->order = $data['order'];
        $template->save();

        return response(['success' => true, 'data' => $template]);
    }



    public function update(GroupTemplate $group_template){
        $data=\request()->validate([
            'slug'=>['required'],
            'title'=>['required'],
            'text'=>['required'],
            'image'=>['required'],
            'groups_templates_category_id'=>['required','exists:templates_categories,id'],
            'order'=>['required'],
            'playback_mode_icon'=>[''],
            'direction_icon'=>[''],
            'bestfor_icon'=>[''],
            'popular_icon'=>[''],
        ]);

        $group_template->update($data);

 

        return response(['success' => true, 'data' => $group_template]);
    }

}
