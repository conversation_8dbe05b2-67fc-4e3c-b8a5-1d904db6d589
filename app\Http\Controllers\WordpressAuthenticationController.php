<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Shop;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;
use App\Jobs\RenewFreeSubscription;
use App\Http\Traits\UpdateS3File;
use Illuminate\Support\Facades\Log;

class WordpressAuthenticationController extends Controller
{
    //
    use UpdateS3File;
    
    public function __construct()
    {
        $this->middleware('auth.redirect')->except('logout');
    }

    public function index(Request $request)
    {

        $admin_email = $request->query('admin_email');
        $wordpress_site_id = $request->query('site_id');
        $wordpress_site_name = $request->query('site_name');
        $wordpress_url = $request->query('url');
        $woocommerce_active = $request->query('woocommerce_active');

        Session::put('wordpress_site_id', $wordpress_site_id);
        Session::put('wordpress_site_name', $wordpress_site_name);
        Session::put('wordpress_url',$wordpress_url);
        Session::put('woocommerce_active',$woocommerce_active);
        return view('wordpress.auth', compact('admin_email', 'wordpress_site_id', 'wordpress_url'));
    }

    //callback for woocommerce tokens request
    public function wcAuth(Request $request)
    {
        //get tokens
        $data = $request->all();
        $consumer_key = $data["consumer_key"];
        $consumer_secret = $data["consumer_secret"];
        $user_id = (int)$data["user_id"];
        $key_id = $data["key_id"];

        $current_shop = Shop::where('user_id','=', $user_id)->first();
        $current_shop->wordpress_consumer_key = $consumer_key;
        $current_shop->wordpress_consumer_secret = $consumer_secret;
        $current_shop->wordpress_key_id = $key_id;
        $current_shop->save();
        //store tokens in according shop
        return response()->json(['success' => 'success'], 200);
    }

    public function registerWordpress(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|unique:users',
            'password' => 'required|min:6',
        ]);
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }


        $wordpress_site_id = Session::get('wordpress_site_id');
        $wordpress_site_name = Session::get('wordpress_site_name');
        $wordpress_url = Session::get('wordpress_url');
        $woocommerce_active = Session::get('woocommerce_active');
        

        //creating user and shop
        $user = User::create([
            'email' => $request->email,
            'password' => bcrypt($request->password),
            'name' => $wordpress_site_name
            // 'email' => '<EMAIL>'
        ]);

        

        $shop = Shop::updateOrCreate(
            [
                'user_id' => $user->id,
                'wordpress_shop_id' => $wordpress_site_id
            ],
            [
                'wordpress_domain' => $wordpress_url,
                'type' => 'wordpress'
            ]
        );

        //begin subscription
        Subscription::create([
            'billable_id' => $user->id,
            'billable_type' => "App\Models\User",
            'name' => "default",
            'paddle_id' => null,
            'paddle_status' => "active",
            'paddle_plan' => 'Free Plan',
            'quantity' => 1,
            'trial_ends_at' => null,
            'paused_from' => null,
            'ends_at' => Carbon::now()->addDays(30),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
            'receipt_url' => "",
            'amount' => 0,
        ]);
        RenewFreeSubscription::dispatch($user)->delay(Carbon::now()->addDays(30)->addMinute());
        $user->free_subscription = 1;
        $user->is_all_consumed= 0;
        $user->is_partially_consumed= 0;
        $user->total_consumption = 0;
        $user->save();
        $this->re_add_files_to_s3($user->id);


        //woocommerce part
        
        $woocommerce_active_boolean = $woocommerce_active === 'true'? true: false;
        if($woocommerce_active_boolean){
            $store_url = $wordpress_url;
            $endpoint = '/wc-auth/v1/authorize';
            $params = [
                'app_name' => 'Smart Stories',
                'scope' => 'read_write',
                'user_id' => $user->id,
                'return_url' => config('app.url').'login',
                'callback_url' => config('app.url').'wordpress/woocommerce'
                // 'return_url' => 'https://fc53-178-135-0-209.ngrok-free.app/',
                // 'callback_url' => 'https://fc53-178-135-0-209.ngrok-free.app/wordpress/woocommerce'
            ];
            $query_string = http_build_query( $params );

            $full_url = $store_url . $endpoint . '?' . $query_string;
            return redirect($full_url);
        }
        else{
            return redirect('/');
        }

    
    }


    public function deactivatePluginWordpressWebhook(Request $request)
    {
        $postData = $request->getContent();
        $webhook_response = json_decode($postData, JSON_PRETTY_PRINT);
        $site_id = $webhook_response["site_id"];
        $event = $webhook_response["event"];
        $plugin = $webhook_response["plugin"];
        $email = $webhook_response["email"];
        $current_shop_data = Shop::where('wordpress_shop_id', $site_id)->first();
        if($current_shop_data != null){
            $user_id = $current_shop_data->user_id;
            //here we are verifying the user email also to prevent attacks.
            $current_user = User::findOrFail($user_id);
            $current_user_email = $current_user->email;
            if ( !is_null($current_user) && !is_null($email) && $current_user_email == $email) {
                //putting previous subscription to swaped
                $subscriptions = Subscription::where('billable_id','=', $user_id)
                                ->where('paddle_status', '=', "active");

                if($subscriptions->count() > 0){
                    $subscriptions->update(['paddle_status' => 'swaped']);
                }
                //updating uninstall
                $user = User::find($user_id);
                $user->app_uninstalled_status = 'Uninstalled';
                $user->	app_uninstalled_at = now();
                $user->save();
            }
            
        }

    }

    public function activatePluginWordpressWebhook(Request $request)
    {
        $postData = $request->getContent();
        $webhook_response = json_decode($postData, JSON_PRETTY_PRINT);
        $site_id = $webhook_response["site_id"];
        $event = $webhook_response["event"];
        $plugin = $webhook_response["plugin"];
        $email = $webhook_response["email"];
        $current_shop_data = Shop::where('wordpress_shop_id', $site_id)->first();
        if($current_shop_data != null){
            $user_id = $current_shop_data->user_id;
            //here we are verifying the user email also to prevent attacks.
            $current_user = User::findOrFail($user_id);
            $current_user_email = $current_user->email;
            
            if ( !is_null($current_user) && !is_null($email) && $current_user_email == $email) {
                //resetting the uninstall tag for the user 
                $current_user->app_uninstalled_status = null;
                $current_user->	app_uninstalled_at = null;
                $current_user->free_subscription = 1;
                $current_user->is_all_consumed= 0;
                $current_user->is_partially_consumed= 0;
                $current_user->total_consumption = 0;
                $current_user->save();
                //putting previous subscription to swaped
                $subscriptions = Subscription::where('billable_id','=', $user_id)
                                ->where('paddle_status', '=', "active");
                if($subscriptions->count() > 0){
                    $subscriptions->update(['paddle_status' => 'swaped']);
                }
                //begin subscription
                Subscription::create([
                    'billable_id' => $current_user->id,
                    'billable_type' => "App\Models\User",
                    'name' => "default",
                    'paddle_id' => null,
                    'paddle_status' => "active",
                    'paddle_plan' => 'Free Plan',
                    'quantity' => 1,
                    'trial_ends_at' => null,
                    'paused_from' => null,
                    'ends_at' => Carbon::now()->addDays(30),
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                    'receipt_url' => "",
                    'amount' => 0,
                ]);
                RenewFreeSubscription::dispatch($current_user)->delay(Carbon::now()->addDays(30)->addMinute());
                
            }
        }
    }
}
