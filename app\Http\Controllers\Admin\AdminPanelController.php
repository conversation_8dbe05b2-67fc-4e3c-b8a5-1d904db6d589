<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use Illuminate\Support\Facades\Http;
use Log;

class AdminPanelController extends Controller
{ 
    public function index()
    { 
        $subscribers = Subscription::with('user')->get();
        
        return view('admin.panel',[
            'subscribers' => $subscribers
        ]);
    }


    public function show()
    { 
        return redirect($this->view_receipt());
    }
 
}