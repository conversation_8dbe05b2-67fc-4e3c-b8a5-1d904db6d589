<?php

namespace App\Http\Controllers\Api\Shopify;

use App\Domain\Shopify\Services\ShopSession;
use App\Http\Controllers\Controller;
use App\Models\Shop;
use App\Models\User;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Traits\ShopifyTrait;

class GenerateTokenController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     *
     */
    use ShopifyTrait;

    public function __invoke(Request $request)
    {

        $user = \Auth::user();
        $api_key = env('APP_API_KEY');
        $shared_secret = env('APP_SHARED_SECRET');
        $params = \request()->all(); // Retrieve all request parameters
        $hmac = request('hmac'); // Retrieve HMAC request parameter

        $params = array_diff_key($params, array('hmac' => '')); // Remove hmac from params
        ksort($params); // Sort params lexographically

        $computed_hmac = hash_hmac('sha256', http_build_query($params), $shared_secret);

// Use hmac data to check that the response is from Shopify or not
        if (hash_equals($hmac, $computed_hmac)) {

            // Set variables for our request
            $query = array(
                "client_id" => $api_key, // Your API key
                "client_secret" => $shared_secret, // Your app credentials (secret key)
                "code" => $params['code'] // Grab the access key from the URL
            );

            // Generate access token URL
            $access_token_url = "https://" . $params['shop'] . "/admin/oauth/access_token";


            // Configure curl client and execute request
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_URL, $access_token_url);
            curl_setopt($ch, CURLOPT_POST, count($query));
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($query));
            $result = curl_exec($ch);
            curl_close($ch);

            // Store the access token in variable
            $result = json_decode($result, true);
            $access_token = $result['access_token'];
            Session::put('shop',$params['shop']);
            Session::put('access_token', $access_token);
            $formatted_shop = str_replace(".myshopify.com", "", $params['shop']);
            $uninstall_result = $this->uninstallAppWebhook($formatted_shop, $access_token);
            //if user is logged in
            if ($user) {
                //if user fields are empty
                $user_shop = Shop::where('user_id', $user->id)->count();
                if(!$user_shop && $user->shopify_domain == "" && $user->shopify_token == ""){
                    $userWithDomainCount = User::where('shopify_domain', $params['shop'])->count();
                    if($userWithDomainCount == 0){
                        //if the store is not taken then assign the store to the user
                        $user->update([
                            'shopify_token' => $access_token,
                            'shopify_domain' => $params['shop'],
                            'app_uninstalled_status' => null,
                            'app_uninstalled_at' => null
                        ]);
                        Shop::updateOrCreate(
                            [
                                'user_id' => $user->id,
                                'shopify_domain' => $params['shop']
                            ],
                            [
                                'shopify_token' => $access_token,
                                'type' => 'shopify'
                            ]
                        );
                    }
                }
                elseif($user->shopify_domain == $params['shop']){
                    //if the store is correct then update the access_token
                    $user->update([
                        'shopify_token' => $access_token,
                        'shopify_domain' => $params['shop'],
                        'app_uninstalled_status' => null,
                        'app_uninstalled_at' => null
                    ]);
                    Shop::updateOrCreate(
                        [
                            'user_id' => $user->id,
                            'shopify_domain' => $params['shop']
                        ],
                        [
                            'shopify_token' => $access_token,
                            'type' => 'shopify'
                        ]
                    );
                }
                else{
                  //finding current shop user 
                  $userWithDomain = User::where('shopify_domain', $params['shop'])->get();
                  $isDomainInDb = $userWithDomain->count();
                  //if user found then login
                  if($isDomainInDb){
                     //update the token in case token is different
                     if($userWithDomain[0]->shopify_token && ($userWithDomain[0]->shopify_token != $access_token)){
                        $userWithDomain[0]->update([
                            'shopify_token' => $access_token,
                            'app_uninstalled_status' => null,
                            'app_uninstalled_at' => null
                        ]); 
                        Shop::updateOrCreate(
                            [
                                'user_id' => $userWithDomain[0]->id,
                                'shopify_domain' => $params['shop']
                            ],
                            [
                                'shopify_token' => $access_token,
                                'type' => 'shopify'
                            ]
                        );
                        
                    }   
                    auth()->guard('web')->login($userWithDomain[0]);
                  }
                  //if user not found then logout current user
                  else{
                    auth()->guard('web')->logout();
                  }
                }
                
            }

            if(auth()->user() == null){
                //checking if current shop is in DB 
                //finding current shop user 
                $userWithDomain = User::where('shopify_domain', $params['shop'])->get();
                $isDomainInDb = $userWithDomain->count();
                //if user found then login
                if($isDomainInDb){
                     //update the token in case token is different
                     if($userWithDomain[0]->shopify_token && ($userWithDomain[0]->shopify_token != $access_token)){
                        $userWithDomain[0]->update([
                            'shopify_token' => $access_token,
                            'app_uninstalled_status' => null,
                            'app_uninstalled_at' => null
                        ]); 
                        Shop::updateOrCreate(
                            [
                                'user_id' => $userWithDomain[0]->id,
                                'shopify_domain' => $params['shop']
                            ],
                            [
                                'shopify_token' => $access_token,
                                'type' => 'shopify'
                            ]
                        );
                        
                    }   
                    //if in shop start the users's session
                    auth()->guard('web')->login($userWithDomain[0]);
                    return redirect('/?shop='.$params['shop']);
                }
                else{
                    //if user not found then logout current user
                    return redirect('/install-thank-you?shop='.$params['shop']);
                }
                // 
            }
            else{
                return redirect('/?shop='.$params['shop']);
            };
        
        }  
        
    }
}
