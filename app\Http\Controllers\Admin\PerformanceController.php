<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use App\Http\Resources\PerformanceResource;
use App\Http\Resources\PerformancesResource;
use App\Models\Performance;
use App\Models\StatsBars;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class PerformanceController extends Controller
{
    public function index()
    {
        $statsBars = QueryBuilder::for(StatsBars::class)
            ->with('user')
            ->allowedFilters(['id', 'timeline', 'impressions', 'created_at'])
            ->allowedSorts(['id', 'impressions', 'created_at'])
            ->orderBy('created_at', 'DESC')
            ->paginate(request('per_page', 10));

        return PerformanceResource::collection($statsBars);
    }

    public function show($id)
    {
        $statsBars = StatsBars::with('user')->findOrFail($id);

        return PerformanceResource::collection($statsBars);
    }

    public function getPerformances()
    {
        $performances = QueryBuilder::for(Performance::class)
            ->with('user')
            ->allowedFilters(['id', 'impressions', 'created_at', AllowedFilter::scope('filter_by_date'), 'plan'])
            ->allowedSorts(['id', 'impressions', 'total_views', 'created_at']);

        $sum_performances = null;
        if( request('filter') && !in_array( request('filter')['filter_by_date'], ['today', 'yesterday'] ) ){
            switch (request('filter')['filter_by_date']){
                case 'last_7_days':
                case 'last_30_days':
                case 'last_60_days':
                case 'last_90_days':
                    $sum_performances = $performances->select(
                        'user_id', 
                        DB::raw('SUM(user_videos_count) as total_videos_count'),
                        DB::raw('SUM(impressions) as total_impressions'),
                        DB::raw('SUM(total_views) as total_views'),
                        DB::raw('SUM(total_unique_views) as total_unique_views'),
                        DB::raw('SUM(view_rate) as total_view_rate'),
                        DB::raw('SUM(average_time_watched) as total_average_time_watched'),
                        DB::raw('SUM(conversion_rate_total) as conversion_rate_total'),
                        DB::raw('SUM(conversion_rate) as total_conversion_rate'),
                    )
                    ->groupBy('user_id')
                    ->paginate(request('per_page', 10));
                    // $sum_performances = $performances->groupBy('user.id')
                    //     ->map(function ($performance) {
                    //         return  [
                    //             'id' => $performance->first()['id'],
                    //             'user' => $performance->first()['user'],
                    //             'impressions' => $performance->sum('impressions'),
                    //             'total_views' => $performance->sum('total_views'),
                    //             'total_unique_views' => $performance->sum('total_unique_views'),
                    //             'view_rate' => $performance->sum('view_rate'),
                    //             'average_time_watched' => $performance->sum('average_time_watched'),
                    //             'conversion_rate_total' => $performance->sum('conversion_rate_total'),
                    //             'conversion_rate' => $performance->sum('conversion_rate'),
                    //             'created_at' => $performance->first()['created_at'],
                    //             'updated_at' => $performance->first()['updated_at'],
                    //         ];
                    //     });
            }
        }

        if($sum_performances){
            return [ 'data' => [...$sum_performances] ];
        }
        return PerformancesResource::collection($performances->orderBy('created_at', 'DESC')->paginate(request('per_page', 10)));
    }
}
