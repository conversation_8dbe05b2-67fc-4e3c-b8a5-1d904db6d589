<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Shop;
use App\Models\User;
use Illuminate\Http\Request;
use Str;

class AnonymizeUserController extends Controller
{
    public function store($user_id)
    {
        $user=User::find($user_id);
        \Log::info($user);

        $user->name = 'Anonymous';
        $user->email = 'anon' . Str::random(10) . '@smartstories.io';
        $user->shopify_domain = 'shop' . Str::random(10) . '.myshopify.com';
        $user->shopify_token = 'token' . Str::random(10);
//        $user->update([
//            'name' => 'Anonymous',
//            'email' => 'anon' . Str::random(10) . '@smartstories.io',
//            'shopify_domain' => 'shop' . Str::random(10) . '.myshopify.com',
//            'shopify_token' => 'token' . Str::random(10),
//        ]);
        $user->save();
        Shop::where('user_id', $user->id)->delete();

        return response(['success' => true]);


    }
}
