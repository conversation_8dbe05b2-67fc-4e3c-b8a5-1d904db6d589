<?php

namespace App\Http\Controllers;

use Throwable;
use App\Models\Group;
use App\Models\Story;
use App\Models\StoryMedia;
use Illuminate\Http\Request;
use App\Http\Traits\ShopifyTrait;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class GroupsController extends Controller
{
    use ShopifyTrait;
    public function tree($group)
    {
        $group = Group::find($group);
        if($group->user_id == Auth::user()->id){
            return view('group-tree',[
                'group' => $group
            ]);
        }else{
            return redirect()->intended('/');
        }

    }

    public function details_fast(Group $group){
        if($group->user_id == Auth::user()->id){
            //Log::info('@@@WH Time Start: ', [time()]);
            $main_shop = $shop = $token = $storyselected = $story = $appBlockSupport = '';
            $themes = $collections = $products = [];
            $main_shop = auth()->user()->shopify_domain;
            $shop = str_replace(".myshopify.com", "",$main_shop);

            $token = auth()->user()->shopify_token;

            if($group->channel == 'shopify'){
                $themes = $this->getAllThemes($shop, $token);
                $collections = $this->getCollections($shop, $token);
                $products = $this->getProducts($main_shop, $token);

                $role = 'main';
                $active_theme = array_filter($themes, function($theme) use($role) {
                    return (isset($theme['role']) and $theme['role'] == $role);
                });
                //here i am reindexing the array since it is returning the element with its index, to be able to detect the element at all cases i am reindexing it.
                $active_theme = array_values($active_theme);
                $themes_select = $active_theme[0]['id'];

                $themeId = $themes_select;

                $appBlockSupport = $this->verifyAppBlocksSupport($shop, $token, $themeId);
                if($group->type == 'autogenerated'){
                }else{
                    if (request('storyid')) {
                        $storyselected = Story::find(request('storyid'));
                        $story = $storyselected;
                    } else if (request('mediaid')) {
                        $media = StoryMedia::find(request('mediaid'));
                        $storyselected = $media->story;
                    }
                    $group->setRelation('stories',  $group->stories()->withViewsCount(null, null, true)->get());
                }


            }else{
                if (request('storyid')) {
                    $storyselected = Story::find(request('storyid'));
                    $story = $storyselected;
                } else if (request('mediaid')) {
                    $media = StoryMedia::find(request('mediaid'));
                    $storyselected = $media->story;
                }
                $group->setRelation('stories',  $group->stories()->withViewsCount(null, null, true)->get());
            }
            return view('group-details-new',compact('group','main_shop','shop','token','themes','collections','products','storyselected','story','appBlockSupport'));
        }else{
            return redirect()->intended('/');
        }
    }
    public function details($group)
    {
        $group = Group::find($group);
        if($group->user_id == Auth::user()->id){
            return view('group-details',[
                'group' => $group
            ]);
        }else{
            return redirect()->intended('/');
        }
    }
    public function details2($group)
    {
        $group = Group::find($group);
        // dd($group->user_id);
        if($group->user_id == Auth::user()->id){
            return view('group-details2',[
                'group' => $group
            ]);
        }else{
            return redirect()->intended('/');
        }
    }
}
