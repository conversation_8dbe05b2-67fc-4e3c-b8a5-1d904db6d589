<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\EventToken;
use App\Models\Group;
use App\Models\Story;
use App\Models\StoryMedia;
use Carbon\Carbon;
use Hash;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class EventsController extends Controller
{
    public function generate_token(Request $request)
    {
        $event_token = EventToken::create([
            'token' => Hash::make(Carbon::now())
        ]);

        return $event_token;
    }

    public function store(Request $request)
    {
        //CHECK GROUP AND CHANGE VALIDATION IF AUTOGENERATED. DON'T CHECK IF STORY_ID AND STORY_MEDIA_ID EXIST
        $group = Group::findOrFail(request('group_id'));
        if ($group->channel == "shopify" && $group->type == "autogenerated") {
            $validation_array = [
                'type' => 'required|in:view,impression,watched,conversion_rate,interaction',
                'interaction_type' => '',
                'time' => '',
                'story_id' => 'nullable',
                'group_id' => 'required|exists:groups,id',
                'story_media_id' => 'nullable',
                'served_token' => '',
                'event_id' => 'exists:events,id',
                'product_id' => '',
                'product_price' => '',
                'checkout_id' => '',
                'url' => '',
                'player' => '',
                'player_version' => '',
                'template' => '',
                'theme_id' => '',
                'browser' => '',
                'resolution' => '',
                'shop_id' => '',
                'shop_user_identifier' => ''
            ];
        } else {
            $validation_array = [
                'type' => 'required|in:view,impression,watched,conversion_rate,interaction',
                'interaction_type' => '',
                'time' => '',
                'story_id' => 'nullable|exists:stories,id',
                'group_id' => 'required|exists:groups,id',
                'story_media_id' => 'nullable|exists:story_media,id',
                'served_token' => '',
                'event_id' => 'exists:events,id',
                'product_id' => '',
                'product_price' => '',
                'checkout_id' => '',
                'url' => '',
                'player' => '',
                'player_version' => '',
                'template' => '',
                'theme_id' => '',
                'browser' => '',
                'resolution' => '',
                'shop_id' => '',
                'shop_user_identifier' => ''
            ];
        }
        $event = $request->validate($validation_array);

        $event['ip'] = $request->getClientIp();

        // $event['duplicated'] = Event::where('ip', $event['ip'])
        //     ->where('type', $event['type'])
        //     ->where('group_id', $event['group_id'])
        //     ->where('story_id', isset($event['story_id']) ?  $event['story_id'] : null)
        //     ->where('story_media_id', isset($event['story_media_id']) ? $event['story_media_id'] : null)
        //     ->count() >= 10 ? true : false;

        $event['duplicated'] = $this->checkIfDuplicated(
            $event['ip'],
            $event['type'],
            $event['group_id'],
            isset($event['story_id']) ? $event['story_id'] : null,
            isset($event['story_media_id']) ? $event['story_media_id'] : null
        ) >= 10 ? true : false;

        $event['verified'] = isset($event['served_token']) ? true : false;

        //CLEAR MEDIA ID AND STORY ID IF SHOPIFY AUTOGENERATED
        if ($group->channel == "shopify" && $group->type == "autogenerated") {
            unset($event['story_media_id']);
            unset($event['story_id']);
        }
        $type = $event['type'];
        $this->$type($event);

    }

    public function interaction($event)
    {
        $event = $this->createEventWithApiCall($event);
    }

    public function view($event)
    {
        $event = $this->createEventWithApiCall($event);
        if (isset($event->story_media_id)) {
            StoryMedia::find($event->story_media_id)->increment('views');
        }
        if (isset($event->story_id)) {
            Story::find($event->story_id)->increment('views');
        }
        Group::find($event->group_id)->increment('views');
    }

    public function impression($event)
    {
        $event = $this->createEventWithApiCall($event);
        //Log::info('impression',['events'=>$event]);
        if (isset($event->story_media_id)) {
            StoryMedia::find($event->story_media_id)->increment('impressions');
        }
        if (isset($event->story_id)) {
            Story::find($event->story_id)->increment('impressions');
        }
        Group::find($event->group_id)->increment('impressions');
    }

    public function watched($event)
    {
        $event = $this->createEventWithApiCall($event);
        if (isset($event->story_media_id)) {
            StoryMedia::find($event->story_media_id)->increment('average_watch');
        }
        if (isset($event->story_id)) {
            Story::find($event->story_id)->increment('average_watch');
        }
        Group::find($event->group_id)->increment('average_watch');
    }

    public function conversion_rate(Request $request)
    {
        // getting request body
        $line_items_object = json_decode($request->getContent(), JSON_PRETTY_PRINT);
        $ss_token = $line_items_object["ss_token"];
        $checkout_id = $line_items_object["checkout_id"];
        $smart_stories_products = $line_items_object["smart_stories_products"];
        //getting user interactions from our db
        $startDate = now()->subDays(7)->startOfDay();
        $endDate = now()->endOfDay();
        $current_rows_array = $this->getCurrentRows($ss_token, $startDate, $endDate);
        // $current_rows = Event::where('served_token', $ss_token)
        //                ->whereBetween('created_at', [$startDate, $endDate])
        //                ->get();
        // Convert the collection to an array
        //$current_rows_array = $current_rows->toArray();
        $matching_rows = [];
        $temp = false;
        //finding matches between the user's interactions and the line items
        foreach ($smart_stories_products as $smart_stories_product) {
            $matching_rows = array_filter($current_rows_array, function ($toCheck) use ($smart_stories_product) {
                return $toCheck->product_id == $smart_stories_product["product_id"];
            });
            //match found!
            if ($matching_rows != false) {
                // reindexing the array
                $matching_rows = array_values($matching_rows);
                //checking if we already registered this checkout
                $checkout_present = array_filter($matching_rows, function ($toCheck2) use ($checkout_id) {
                    return $toCheck2->checkout_id == $checkout_id;
                });
                // the following array is created in order to seperated the conversion rate of each group
                $group_ids = [];
                if (count($checkout_present) <= 0) {
                    foreach ($matching_rows as $matching_row) {
                        array_push($group_ids, $matching_row->group_id);
                        //creating event
                        $verified = isset($matching_row->served_token) ? true : false;
                        if ($matching_row->type != "conversion_rate") {
                            $eventData = [
                                'type' => 'conversion_rate',
                                'time' => '',
                                'story_id' => $matching_row->story_id,
                                'group_id' => $matching_row->group_id,
                                'story_media_id' => $matching_row->story_media_id,
                                'served_token' => $matching_row->served_token,
                                'event_id' => $matching_row->id,
                                'product_id' => $matching_row->product_id,
                                'product_price' => $smart_stories_product["product_price"],
                                'checkout_id' => $checkout_id,
                                'url' => $matching_row->url,
                                'ip' => $matching_row->ip,
                                'player' => $matching_row->player,
                                'player_version' => $matching_row->player_version,
                                'template' => $matching_row->template,
                                'theme_id' => $matching_row->theme_id,
                                'browser' => $matching_row->browser,
                                'resolution' => $matching_row->resolution,
                                'shop_id' => $matching_row->shop_id,
                                'shop_user_identifier' => $matching_row->shop_user_identifier,
                                'verified' => $verified
                            ];
                            $this->createCvWithApiCall($eventData);

                            //    Event::create([
                            //        'type' => 'conversion_rate',
                            //        'time' => '',
                            //        'story_id'=>  $matching_row["story_id"],
                            //        'group_id' => $matching_row["group_id"],
                            //        'story_media_id' => $matching_row["story_media_id"],
                            //        'served_token' => $matching_row["served_token"],
                            //        'event_id' => $matching_row["id"],
                            //        'product_id' => $matching_row["product_id"],
                            //        'product_price' => $smart_stories_product["product_price"],
                            //        'checkout_id' => $checkout_id,
                            //        'url' => $matching_row["url"],
                            //        'ip' => $matching_row["ip"],
                            //        'player' => $matching_row["player"],
                            //        'player_version' => $matching_row["player_version"],
                            //        'template' => $matching_row["template"],
                            //        'theme_id' => $matching_row["theme_id"],
                            //        'browser' => $matching_row["browser"],
                            //        'resolution' => $matching_row["resolution"],
                            //        'shop_id' => $matching_row["shop_id"],
                            //        'shop_user_identifier' => $matching_row["shop_user_identifier"],
                            //        'verified' => $verified
                            //    ]);
                        }

                    }
                }

            }
        }


        return response()->json([
            'message' => 'success'
        ], 200);


    }

    public static function createEventWithApiCall($event)
    {

        $curl = curl_init();
        $postgreUrl = config('app.postgresql_url');
        $apiUrl = "{$postgreUrl}/api/events";

        curl_setopt_array($curl, array(
            CURLOPT_URL => $apiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($event),
            CURLOPT_HTTPHEADER => array(
                'Accept: application/json',
                'x-api-key: SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
                'Content-Type: application/json'
            ),
        ));

        $mh = curl_multi_init();
        curl_multi_add_handle($mh, $curl);

        $active = null;
        do {
            curl_multi_exec($mh, $active);
            // usleep(100);
        } while ($active > 0);

        $response = curl_multi_getcontent($curl);
        //Log::info('response', ['createEventWithApiCall' => json_decode($response)]);
        $createdEvent = json_decode($response)->data;
      //  Log::info('response', ['createEventWithApiCall' => json_decode($response)]);
        curl_multi_remove_handle($mh, $curl);
        curl_multi_close($mh);
        return $createdEvent;
//        if (curl_errno($curl)) {
//            self::createEventWithApiCall($event);
//        } else {
//            $httpStatus = curl_getinfo($curl, CURLINFO_HTTP_CODE);
//            if ($httpStatus == 500)
//                self::createEventWithApiCall($event);
//            else {
//
//            }
//        }
//
//        return [];


    }

    public static function createCvWithApiCall($event)
    {

        $curl = curl_init();
        $postgreUrl = config('app.postgresql_url');
        $apiUrl = "{$postgreUrl}/api/events";

        curl_setopt_array($curl, array(
            CURLOPT_URL => $apiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($event),
            CURLOPT_HTTPHEADER => array(
                'Accept: application/json',
                'x-api-key: SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
                'Content-Type: application/json'
            ),
        ));

        $mh = curl_multi_init();
        curl_multi_add_handle($mh, $curl);

        $active = null;
        do {
            curl_multi_exec($mh, $active);
            usleep(100);
        } while ($active > 0);

        $response = curl_multi_getcontent($curl);
        curl_multi_remove_handle($mh, $curl);
        curl_multi_close($mh);
    }

    public static function getCurrentRows($ss_token, $startDate, $endDate)
    {

        $curl = curl_init();
        $postgreUrl = config('app.postgresql_url');
        $apiUrl = $postgreUrl."/api/eventsQuery/currentRows?served_token=".$ss_token."&startDate=".urlencode($startDate)."&endDate=".urlencode($endDate);

        $multiHandle = curl_multi_init();
        $curlHandles = [];

        Log::info('current rows',['url'=>$apiUrl]);
        curl_setopt_array($curl, array(
            CURLOPT_URL => $apiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Accept: application/json',
                'x-api-key: SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
                'Content-Type: application/json'
            ),
        ));
        curl_multi_add_handle($multiHandle, $curl);
        $curlHandles[] = $curl;

        $running = null;
        do {
            curl_multi_exec($multiHandle, $running);
        } while ($running > 0);

        foreach ($curlHandles as $handle) {
            $response = curl_multi_getcontent($handle);
            Log::info('response', ['getCurrentRows' => json_decode($response)]);

            $apiData = json_decode($response)->data;
            $apiData = is_array($apiData) ? $apiData : [$apiData];
            curl_multi_remove_handle($multiHandle, $handle);
            curl_close($handle);
        }
        curl_multi_close($multiHandle);

        return $apiData;

    }

    public static function checkIfDuplicated($ip, $type, $group_id, $story_id = null, $story_media_id = null)
    {
        $curl = curl_init();
        $multiHandle = curl_multi_init();
        $curlHandles = [];

        $postgreUrl = config('app.postgresql_url');
        $apiUrl = "{$postgreUrl}/api/eventsQuery/duplicated?ip=$ip&type=$type&group_id=$group_id&story_id=$story_id&story_media_id=$story_media_id";

        curl_setopt_array($curl, array(
            CURLOPT_URL => $apiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Accept: application/json',
                'x-api-key: SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
                'Content-Type: application/json'
            ),
        ));

        $curlHandles[] = $curl;
        curl_multi_add_handle($multiHandle, $curl);

        // Execute all requests asynchronously
        $running = null;
        do {
            curl_multi_exec($multiHandle, $running);
        } while ($running);


        foreach ($curlHandles as $curlHandle) {
            $response = curl_multi_getcontent($curlHandle);
            $apiData = json_decode($response, true)['data'];
            curl_multi_remove_handle($multiHandle, $curlHandle);
            curl_close($curlHandle);
        }

        curl_multi_close($multiHandle);
        return $apiData;
    }

}
