<?php

namespace App\Http\Controllers\Api\Groups;

use App\Http\Controllers\Controller;
use App\Models\Group;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class GroupController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function __invoke(Request $request, Group $group)
    {
       // $user = User::where('id', $group->user_id)->firstOrFail();
        //if (!$user->reached_limit()) {

            if ($group->preview_secret === request('preview_secret')) {
                if (!Storage::disk('s3_group')->exists($group->secret . '_' . \request('preview_secret') . '.json')) {
                    $group->load('stories.media.actions', 'stories.media.playerCustomElements', 'groupCustomElements');
                    Storage::disk('s3_group')->put($group->secret . '_' . \request('preview_secret') . '.json', $group);
                }
                return response(['url' => config('app.cloud_front_link') . $group->secret  . '.json']);

            }

            if (!Storage::disk('s3_group')->exists($group->secret . '.json')) {
                views($group)->record();

                $group = Group::query()
                    ->where('id', $group->id)
                    ->with([
                        'groupCustomElements',
                        'stories' => function ($query) {
                            $query
                                ->whereHas('media', function ($query) {
                                    $query->where('activated_at', '<=', now());
                                })
                                ->where('activated_at', '<=', now());
                        },
                        'stories.media' => function ($query) {
                            $query->where('activated_at', '<=', now());
                        },
                        'stories.media.actions',
                        'stories.media.playerCustomElements',
                    ])
                    ->first();
                Storage::disk('s3_group')->put($group->secret . '.json', $group);
            }
            return response(['url' => config('app.cloud_front_link') . $group->secret . '.json']);

       // }
      //  return response(['success' => false, 'message' => 'user has reached the limit']);
    }

}
