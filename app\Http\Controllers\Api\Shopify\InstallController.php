<?php

namespace App\Http\Controllers\Api\Shopify;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use App\Models\User;
class InstallController extends Controller
{
    public function index()
    {

        if (\request()->has('shop')) {

            $shop = request('shop');
            $api_key = env('APP_API_KEY');
            $scopes = "read_orders,write_products,read_products,read_themes,write_themes";
            $redirect_uri = config('app.url')."api/apps/generate-token";


            $install_url = "https://" . $shop . "/admin/oauth/authorize?client_id=" . $api_key . "&scope=" . $scopes . "&redirect_uri=" . urlencode($redirect_uri);
            return Redirect::to($install_url);
        }
        else
        {
            return response(['message'=>'shop is required']);
        }


    }
    
    public function verifyStorePresence()
    {
        if (\request()->has('shop')) {

            $shop = request('shop');
            $shop = $shop.".myshopify.com";
            $shopPresent = User::where('shopify_domain','=',$shop)->count() > 0;
            return response($shopPresent);
        }
        else
        {
            return response(['message'=>'shop is required']);
        }
    }
}
