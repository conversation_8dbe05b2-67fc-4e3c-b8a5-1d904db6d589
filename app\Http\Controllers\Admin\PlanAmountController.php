<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Http\Request;

class PlanAmountController extends Controller
{
    public function index()
    {

        $startOfMonth = Carbon::now()->startOfMonth();
        $endOfMonth = Carbon::now()->endOfMonth();

        $plans = ['Premium Plan', 'Standard Plan', 'Enterprise Plan'];

        $results = [];
        foreach ($plans as $plan) {
            $results[lcfirst(str_replace(' ', '_', $plan))] = [
                'total_amount' => Subscription::where('paddle_plan', $plan)
                    ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
                    ->sum('amount'),
                'subscription_count' => Subscription::where('paddle_plan', $plan)
                    ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
                    ->count(),
            ];
        }

        return response(['success' => true, 'data' => $results]);

    }
}
