<?php

namespace App\Http\Controllers\Admin\CustomFilters;

use DB;
use Illuminate\Database\Eloquent\Builder;
use Spatie\QueryBuilder\Filters\Filter;

class FilterSubscription implements Filter
{
    public function __invoke(Builder $query, $value, string $property)
    {
        $query->whereHas('subscription', function ($contest_query) use ($value) {
            $contest_query->where(['paddle_status'=>'active'])->where('paddle_plan', $value);
        });


    }

}
