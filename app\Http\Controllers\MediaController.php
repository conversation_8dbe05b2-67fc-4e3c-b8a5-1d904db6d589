<?php

namespace App\Http\Controllers;

use App\Models\StoryMedia;
use Illuminate\Http\Request;

class MediaController extends Controller
{
    public function updateOrder(Request $request)
    {
        $order = $request->input('order');    
        foreach ($order as $item) {
            $media = StoryMedia::find($item['id']);
            $media->order_column = $item['position'];
            $media->save();
        
            $updatedMedia = StoryMedia::find($item['id']);
        }
        

        return response()->json(['success' => true]);
    }
}
