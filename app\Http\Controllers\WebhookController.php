<?php

namespace App\Http\Controllers;

use App\Models\Group;
use App\Models\Shopify;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WebhookController extends Controller
{

    public function data_request()
    {
        //Log::info('Shopify Webhook', ['data_request', request()->all()]);
        if (isset($_SERVER['HTTP_X_SHOPIFY_HMAC_SHA256'])) {
            $hmac_header = $_SERVER['HTTP_X_SHOPIFY_HMAC_SHA256'];
            $data = file_get_contents('php://input');
            $verified = Shopify::verify_webhook($data, $hmac_header);
            if($verified)
                return response(['success'=>true]);
            else
                return response(['success'=>false,'message'=>'Request not verified'],401);
        } else {
            return response(['success'=>false,'message'=>'Request not from shopify'],401);
        }
    }

    public function delete_customer_data()
    {
        //Log::info('Shopify Webhook', ['delete_customers_data', request()->all()]);
        if (isset($_SERVER['HTTP_X_SHOPIFY_HMAC_SHA256'])) {
            $hmac_header = $_SERVER['HTTP_X_SHOPIFY_HMAC_SHA256'];
            $data = file_get_contents('php://input');
            $verified = Shopify::verify_webhook($data, $hmac_header);
            if($verified)
                return response(['success'=>true]);
            else
                return response(['success'=>false,'message'=>'Request not verified'],401);
        } else {
            return response(['success'=>false,'message'=>'Request not from shopify'],401);
        }
    }

    public function delete_shop()
    {
        //Log::info('Shopify Webhook', ['delete_shop', request()->all()]);
        if (isset($_SERVER['HTTP_X_SHOPIFY_HMAC_SHA256'])) {
            $hmac_header = $_SERVER['HTTP_X_SHOPIFY_HMAC_SHA256'];
            $data = file_get_contents('php://input');
            $verified = Shopify::verify_webhook($data, $hmac_header);
            if($verified)
                return response(['success'=>true]);
            else
                return response(['success'=>false,'message'=>'Request not verified'],401);
        } else {
            return response(['success'=>false,'message'=>'Request not from shopify'],401);
        }
    }


}
