<?php

namespace App\Http\Controllers\Api\Webhook;

use App\Http\Controllers\Controller;
use App\Models\Group;
use App\Models\User;
use Illuminate\Http\Request;

class UserController extends Controller
{
    public function index()
    {

        $users = User::all()->pluck('id');
        $groups = Group::select(['id','user_id'])->get();

        return response(['users' => $users, 'groups' => $groups]);
    }

    public function users(){
        $users = User::select(['id','name','email','email_verified_at','shopify_domain'])->get();

        return response(['users' => $users]);
    }
}
