<?php

namespace App\Http\Controllers\Admin\CustomFilters;

use Illuminate\Database\Eloquent\Builder;
use Spatie\QueryBuilder\Filters\Filter;

class CustomPaddleStatusFilter implements Filter
{
    public function __invoke(Builder $query, $value, string $property)
    {
        if ($value == 'active') {
             $query->where('paddle_status', '=', 'active');
        }
        else{
             $query->where('paddle_status', '!=', 'active');
        }
    }
}
